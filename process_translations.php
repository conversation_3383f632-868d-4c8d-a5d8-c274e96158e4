<?php

/**
 * <PERSON>ript để xử lý translation: extract hard text, tạo keys, và cập nhật files
 */

require_once 'find_hard_text.php';

class TranslationProcessor
{
    private $hardTexts = [];
    private $translationKeys = [];
    private $viTranslations = [];
    private $enTranslations = [];
    
    public function __construct()
    {
        // Tìm hard text
        $finder = new HardTextFinder();
        $this->hardTexts = $finder->findHardText('.');
        
        // Tạo translation mapping
        $this->generateTranslationMapping();
    }
    
    /**
     * Tạo mapping từ text sang translation key
     */
    private function generateTranslationMapping()
    {
        $usedKeys = [];
        
        foreach ($this->hardTexts as $result) {
            $text = $result['text'];
            $file = $result['file'];
            
            // Tạo key dựa trên nội dung
            $key = $this->generateKey($text, $file);
            
            // Đảm bảo key là duy nhất
            $originalKey = $key;
            $suffix = 1;
            while (isset($usedKeys[$key])) {
                $key = $originalKey . '_' . $suffix;
                $suffix++;
            }
            
            $usedKeys[$key] = true;
            $this->translationKeys[$text] = $key;
            
            // Thêm vào translation arrays
            $this->viTranslations[$key] = $text;
        }
    }
    
    /**
     * Tạo translation key từ text và context
     */
    private function generateKey($text, $file)
    {
        // Xác định file group
        $fileGroup = $this->determineFileGroup($file);
        
        // Chuyển đổi text thành key
        $textKey = $this->textToKey($text);
        
        return $fileGroup . '.' . $textKey;
    }
    
    /**
     * Xác định nhóm file để tổ chức translation
     */
    private function determineFileGroup($file)
    {
        if (strpos($file, 'auth') !== false || strpos($file, 'login') !== false || strpos($file, 'register') !== false) {
            return 'auth';
        }
        if (strpos($file, 'account') !== false) {
            return 'account';
        }
        if (strpos($file, 'deposit') !== false) {
            return 'deposit';
        }
        if (strpos($file, 'news') !== false) {
            return 'news';
        }
        if (strpos($file, 'header') !== false) {
            return 'header';
        }
        if (strpos($file, 'footer') !== false) {
            return 'footer';
        }
        
        return 'common';
    }
    
    /**
     * Chuyển đổi text thành key
     */
    private function textToKey($text)
    {
        // Chuyển về lowercase
        $key = mb_strtolower($text, 'UTF-8');
        
        // Loại bỏ dấu tiếng Việt
        $key = $this->removeVietnameseAccents($key);
        
        // Chỉ giữ lại chữ cái, số và khoảng trắng
        $key = preg_replace('/[^a-zA-Z0-9\s]/', '', $key);
        
        // Thay khoảng trắng bằng underscore
        $key = preg_replace('/\s+/', '_', trim($key));
        
        // Giới hạn độ dài
        $key = substr($key, 0, 50);
        
        // Loại bỏ underscore ở đầu và cuối
        $key = trim($key, '_');
        
        return $key ?: 'text_' . md5($text);
    }
    
    /**
     * Loại bỏ dấu tiếng Việt
     */
    private function removeVietnameseAccents($str)
    {
        $accents = [
            __('common.a') => 'a', __('common.a_1') => 'a', __('common.a_2') => 'a', __('common.a_3') => 'a', __('common.a_4') => 'a',
            __('common.a_5') => 'a', __('common.a_6') => 'a', __('common.a_7') => 'a', __('common.a_8') => 'a', __('common.a_9') => 'a', __('common.a_10') => 'a',
            __('common.a_11') => 'a', __('common.a_12') => 'a', __('common.a_13') => 'a', __('common.a_14') => 'a', __('common.a_15') => 'a', __('common.a_16') => 'a',
            __('common.e') => 'e', __('common.e_1') => 'e', __('common.e_2') => 'e', __('common.e_3') => 'e', __('common.e_4') => 'e',
            __('common.e_5') => 'e', __('common.e_6') => 'e', __('common.e_7') => 'e', __('common.e_8') => 'e', __('common.e_9') => 'e', __('common.e_10') => 'e',
            __('common.i') => 'i', __('common.i_1') => 'i', __('common.i_2') => 'i', __('common.i_3') => 'i', __('common.i_4') => 'i',
            __('common.o') => 'o', __('common.o_1') => 'o', __('common.o_2') => 'o', __('common.o_3') => 'o', __('common.o_4') => 'o',
            __('common.o_5') => 'o', __('common.o_6') => 'o', __('common.o_7') => 'o', __('common.o_8') => 'o', __('common.o_9') => 'o', __('common.o_10') => 'o',
            __('common.o_11') => 'o', __('common.o_12') => 'o', __('common.o_13') => 'o', __('common.o_14') => 'o', __('common.o_15') => 'o', __('common.o_16') => 'o',
            __('common.u') => 'u', __('common.u_1') => 'u', __('common.u_2') => 'u', __('common.u_3') => 'u', __('common.u_4') => 'u',
            __('common.u_5') => 'u', __('common.u_6') => 'u', __('common.u_7') => 'u', __('common.u_8') => 'u', __('common.u_9') => 'u', __('common.u_10') => 'u',
            __('common.y') => 'y', __('common.y_1') => 'y', __('common.y_2') => 'y', __('common.y_3') => 'y', __('common.y_4') => 'y',
            __('common.d') => 'd', __('common.d_1') => 'D'
        ];
        
        return strtr($str, $accents);
    }
    
    /**
     * Tạo file translation tiếng Việt
     */
    public function createVietnameseTranslationFiles()
    {
        // Nhóm translations theo file group
        $groups = [];
        foreach ($this->viTranslations as $key => $text) {
            $parts = explode('.', $key, 2);
            $group = $parts[0];
            $subKey = $parts[1];
            
            if (!isset($groups[$group])) {
                $groups[$group] = [];
            }
            $groups[$group][$subKey] = $text;
        }
        
        // Tạo hoặc cập nhật các file translation
        foreach ($groups as $group => $translations) {
            $this->updateTranslationFile("lang/vi/{$group}.php", $translations);
        }
    }
    
    /**
     * Cập nhật file translation
     */
    private function updateTranslationFile($filePath, $newTranslations)
    {
        // Đọc file hiện tại nếu có
        $existingTranslations = [];
        if (file_exists($filePath)) {
            $existingTranslations = include $filePath;
            if (!is_array($existingTranslations)) {
                $existingTranslations = [];
            }
        }
        
        // Merge với translations mới
        $allTranslations = array_merge($existingTranslations, $newTranslations);
        
        // Sắp xếp theo key
        ksort($allTranslations);
        
        // Tạo nội dung file
        $content = "<?php\n\nreturn [\n";
        foreach ($allTranslations as $key => $value) {
            // Bỏ qua nếu value là array
            if (is_array($value)) {
                continue;
            }

            // Đảm bảo key và value là string
            $key = is_string($key) ? $key : (string)$key;
            $value = is_string($value) ? $value : (string)$value;

            $escapedKey = addslashes($key);
            $escapedValue = addslashes($value);
            $content .= "    '{$escapedKey}' => '{$escapedValue}',\n";
        }
        $content .= "];\n";
        
        // Tạo thư mục nếu chưa có
        $dir = dirname($filePath);
        if (!is_dir($dir)) {
            mkdir($dir, 0755, true);
        }
        
        // Ghi file
        file_put_contents($filePath, $content);
        echo __('common.da_cap_nhat_file_filepathn');
    }
    
    /**
     * Thay thế hard text trong files
     */
    public function replaceHardTextInFiles()
    {
        $processedFiles = [];
        
        // Nhóm theo file
        foreach ($this->hardTexts as $result) {
            $file = $result['file'];
            $text = $result['text'];
            $line = $result['line'];
            
            if (!isset($this->translationKeys[$text])) {
                continue;
            }
            
            $translationKey = $this->translationKeys[$text];
            
            if (!isset($processedFiles[$file])) {
                $processedFiles[$file] = [];
            }
            
            $processedFiles[$file][] = [
                'text' => $text,
                'key' => $translationKey,
                'line' => $line
            ];
        }
        
        // Xử lý từng file
        foreach ($processedFiles as $file => $replacements) {
            $this->processFile($file, $replacements);
        }
    }
    
    /**
     * Xử lý một file để thay thế hard text
     */
    private function processFile($filePath, $replacements)
    {
        if (!file_exists($filePath)) {
            echo __('common.file_khong_ton_tai_filepathn');
            return;
        }
        
        $content = file_get_contents($filePath);
        $originalContent = $content;
        
        // Sắp xếp replacements theo độ dài text giảm dần để tránh thay thế nhầm
        usort($replacements, function($a, $b) {
            return strlen($b['text']) - strlen($a['text']);
        });
        
        foreach ($replacements as $replacement) {
            $text = $replacement['text'];
            $key = $replacement['key'];
            
            // Tạo translation function call
            $translationCall = "__('$key')";
            
            // Thay thế hard text bằng translation call
            $patterns = [
                // Double quotes
                '/"' . preg_quote($text, '/') . '"/',
                // Single quotes  
                "/'" . preg_quote($text, '/') . "'/"
            ];
            
            foreach ($patterns as $pattern) {
                $content = preg_replace($pattern, $translationCall, $content);
            }
        }
        
        if ($content !== $originalContent) {
            // Backup file gốc
            $backupPath = $filePath . '.backup';
            if (!file_exists($backupPath)) {
                copy($filePath, $backupPath);
            }
            
            // Ghi file mới
            file_put_contents($filePath, $content);
            echo __('common.da_thay_the_hard_text_trong_filepathn');
        }
    }
    
    /**
     * Tạo báo cáo
     */
    public function generateReport()
    {
        echo "=== BÁO CÁO XỬ LÝ TRANSLATION ===\n\n";
        echo __('common.tong_so_hard_text') . count($this->hardTexts) . "\n";
        echo __('common.tong_so_translation_keys') . count($this->translationKeys) . "\n\n";
        
        // Nhóm theo file group
        $groups = [];
        foreach ($this->viTranslations as $key => $text) {
            $parts = explode('.', $key, 2);
            $group = $parts[0];
            
            if (!isset($groups[$group])) {
                $groups[$group] = 0;
            }
            $groups[$group]++;
        }
        
        echo __('common.phan_bo_theo_nhomn');
        foreach ($groups as $group => $count) {
            echo "  $group: $count keys\n";
        }
    }
    
    /**
     * Lấy danh sách translations để dịch sang tiếng Anh
     */
    public function getVietnameseTranslations()
    {
        return $this->viTranslations;
    }
}

// Chạy script nếu được gọi trực tiếp
if (basename(__FILE__) === basename($_SERVER['SCRIPT_NAME'])) {
    $processor = new TranslationProcessor();
    $processor->generateReport();
    
    echo __('common.ntao_file_translation_tieng_vietn');
    $processor->createVietnameseTranslationFiles();
    
    echo __('common.nthay_the_hard_text_trong_filesn');
    $processor->replaceHardTextInFiles();
    
    echo __('common.nhoan_thanh_xu_ly_translationn');
}

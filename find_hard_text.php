<?php

/**
 * Script để tìm kiếm hard text tiếng Việt trong các file .php và .blade.php
 */

class HardTextFinder
{
    private $excludedDirs = ['lang', 'vendor', 'node_modules', 'storage', 'bootstrap/cache'];
    private $includedExtensions = ['php', 'blade.php'];
    private $hardTexts = [];
    
    public function __construct()
    {
        // Khởi tạo
    }
    
    /**
     * Tìm tất cả hard text trong thư mục
     */
    public function findHardText($directory = '.')
    {
        $this->scanDirectory($directory);
        return $this->hardTexts;
    }
    
    /**
     * Quét thư mục đệ quy
     */
    private function scanDirectory($dir)
    {
        if (!is_dir($dir)) {
            return;
        }
        
        $items = scandir($dir);
        foreach ($items as $item) {
            if ($item === '.' || $item === '..') {
                continue;
            }
            
            $path = $dir . '/' . $item;
            
            if (is_dir($path)) {
                // Bỏ qua các thư mục không cần thiết
                if (!$this->shouldExcludeDirectory($path)) {
                    $this->scanDirectory($path);
                }
            } else {
                // Kiểm tra file
                if ($this->shouldProcessFile($path)) {
                    $this->processFile($path);
                }
            }
        }
    }
    
    /**
     * Kiểm tra có nên bỏ qua thư mục không
     */
    private function shouldExcludeDirectory($path)
    {
        foreach ($this->excludedDirs as $excludedDir) {
            if (strpos($path, $excludedDir) !== false) {
                return true;
            }
        }
        return false;
    }
    
    /**
     * Kiểm tra có nên xử lý file không
     */
    private function shouldProcessFile($path)
    {
        // Bỏ qua file trong thư mục lang
        if (strpos($path, '/lang/') !== false) {
            return false;
        }
        
        // Kiểm tra extension
        foreach ($this->includedExtensions as $ext) {
            if (substr($path, -strlen($ext)) === $ext) {
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * Xử lý một file để tìm hard text
     */
    private function processFile($filePath)
    {
        $content = file_get_contents($filePath);
        if ($content === false) {
            return;
        }
        
        $lines = explode("\n", $content);
        
        foreach ($lines as $lineNumber => $line) {
            $this->findHardTextInLine($line, $filePath, $lineNumber + 1);
        }
    }
    
    /**
     * Tìm hard text trong một dòng
     */
    private function findHardTextInLine($line, $filePath, $lineNumber)
    {
        // Bỏ qua comment
        if (preg_match('/^\s*\/\//', $line) || preg_match('/^\s*\/\*/', $line) || preg_match('/^\s*\*/', $line)) {
            return;
        }
        
        // Tìm text trong single quotes
        preg_match_all(__('common.aaaaaaaaaaaaaaaaaeeeeeeeeeeeiiiiiooooooooooooooooo_1'), $line, $matches1, PREG_OFFSET_CAPTURE);
        
        // Tìm text trong double quotes
        preg_match_all(__('common.aaaaaaaaaaaaaaaaaeeeeeeeeeeeiiiiiooooooooooooooooo_2'), $line, $matches2, PREG_OFFSET_CAPTURE);
        
        // Xử lý kết quả từ single quotes
        foreach ($matches1[1] as $match) {
            $text = $match[0];
            if ($this->isVietnameseHardText($text)) {
                $this->addHardText($text, $filePath, $lineNumber, $line);
            }
        }
        
        // Xử lý kết quả từ double quotes
        foreach ($matches2[1] as $match) {
            $text = $match[0];
            if ($this->isVietnameseHardText($text)) {
                $this->addHardText($text, $filePath, $lineNumber, $line);
            }
        }
    }
    
    /**
     * Kiểm tra có phải hard text tiếng Việt không
     */
    private function isVietnameseHardText($text)
    {
        // Bỏ qua text rỗng hoặc quá ngắn
        if (empty($text) || strlen($text) < 2) {
            return false;
        }
        
        // Bỏ qua các pattern không phải hard text
        $excludePatterns = [
            '/^[a-zA-Z0-9_\-\.]+$/',  // Chỉ chứa ký tự ASCII
            '/^[0-9\s\-\+\(\)]+$/',   // Chỉ số và ký tự đặc biệt
            '/^https?:\/\//',          // URL
            '/^\/[a-zA-Z0-9_\-\/]*$/', // Path
            '/^\$[a-zA-Z_]/',         // Biến PHP
            '/^[a-zA-Z_][a-zA-Z0-9_]*$/', // Tên biến/function
            '/^__\(/',                // Đã sử dụng translation helper
            '/^@[a-zA-Z]/',           // Blade directive
            '/^[A-Z_]+$/',            // Constants
        ];
        
        foreach ($excludePatterns as $pattern) {
            if (preg_match($pattern, $text)) {
                return false;
            }
        }
        
        // Kiểm tra có chứa ký tự tiếng Việt
        if (preg_match(__('common.aaaaaaaaaaaaaaaaaeeeeeeeeeeeiiiiiooooooooooooooooo_4'), $text)) {
            return true;
        }
        
        return false;
    }
    
    /**
     * Thêm hard text vào danh sách
     */
    private function addHardText($text, $filePath, $lineNumber, $fullLine)
    {
        $this->hardTexts[] = [
            'text' => $text,
            'file' => $filePath,
            'line' => $lineNumber,
            'full_line' => trim($fullLine)
        ];
    }
    
    /**
     * Tạo báo cáo
     */
    public function generateReport()
    {
        echo "=== BÁO CÁO TÌM KIẾM HARD TEXT ===\n\n";
        echo __('common.tong_so_hard_text_tim_thay') . count($this->hardTexts) . "\n\n";
        
        // Nhóm theo file
        $fileGroups = [];
        foreach ($this->hardTexts as $item) {
            $file = $item['file'];
            if (!isset($fileGroups[$file])) {
                $fileGroups[$file] = [];
            }
            $fileGroups[$file][] = $item;
        }
        
        foreach ($fileGroups as $file => $items) {
            echo "File: $file (" . count($items) . " hard texts)\n";
            foreach ($items as $item) {
                echo "  Line {$item['line']}: \"{$item['text']}\"\n";
                echo "    Context: {$item['full_line']}\n";
            }
            echo "\n";
        }
    }
    
    /**
     * Lấy danh sách unique hard texts
     */
    public function getUniqueTexts()
    {
        $unique = [];
        foreach ($this->hardTexts as $item) {
            $unique[$item['text']] = $item['text'];
        }
        return array_values($unique);
    }
}

// Chạy script nếu được gọi trực tiếp
if (basename(__FILE__) === basename($_SERVER['SCRIPT_NAME'])) {
    $finder = new HardTextFinder();
    $hardTexts = $finder->findHardText('.');
    $finder->generateReport();
    
    echo "\n=== DANH SÁCH UNIQUE TEXTS ===\n";
    $uniqueTexts = $finder->getUniqueTexts();
    foreach ($uniqueTexts as $text) {
        echo "\"$text\"\n";
    }
}

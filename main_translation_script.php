<?php

/**
 * <PERSON><PERSON>t chính để xử lý toàn bộ quy trình translation
 * 1. Tìm hard text trong các file .php và .blade.php
 * 2. Tạo translation keys và file lang/vi
 * 3. <PERSON><PERSON><PERSON> sang tiếng Anh và tạo file lang/en
 * 4. Thay thế hard text bằng __() helper trong code
 */

require_once 'find_hard_text.php';
require_once 'process_translations.php';
require_once 'english_translator.php';

class MainTranslationScript
{
    private $finder;
    private $processor;
    private $translator;
    
    public function __construct()
    {
        echo "=== KHỞI TẠO TRANSLATION SCRIPT ===\n\n";
        
        $this->finder = new HardTextFinder();
        $this->processor = new TranslationProcessor();
        $this->translator = new EnglishTranslator();
    }
    
    /**
     * Chạy toàn bộ quy trình
     */
    public function run()
    {
        echo __('common.bat_dau_quy_trinh_xu_ly_translationnn');
        
        // Bước 1: Tìm hard text
        echo "=== BƯỚC 1: TÌM HARD TEXT ===\n";
        $hardTexts = $this->finder->findHardText('.');
        echo __('common.tim_thay_1') . count($hardTexts) . " hard text\n\n";
        
        if (empty($hardTexts)) {
            echo __('common.khong_tim_thay_hard_text_nao_ket_thucn');
            return;
        }
        
        // Hiển thị một số ví dụ
        echo __('common.mot_so_vi_du_hard_text_tim_thayn');
        $examples = array_slice($hardTexts, 0, 5);
        foreach ($examples as $example) {
            echo "  - \"{$example['text']}\__('common.trong_file_examplefile_dong_examplelinen');
        }
        echo "\n";
        
        // Bước 2: Tạo file translation tiếng Việt
        echo "=== BƯỚC 2: TẠO FILE TRANSLATION TIẾNG VIỆT ===\n";
        $this->processor->createVietnameseTranslationFiles();
        echo "\n";
        
        // Bước 3: Tạo file translation tiếng Anh
        echo "=== BƯỚC 3: TẠO FILE TRANSLATION TIẾNG ANH ===\n";
        $this->translator->createEnglishTranslationFiles();
        echo "\n";
        
        // Bước 4: Thay thế hard text trong code
        echo "=== BƯỚC 4: THAY THẾ HARD TEXT TRONG CODE ===\n";
        $this->processor->replaceHardTextInFiles();
        echo "\n";
        
        // Tạo báo cáo cuối cùng
        echo "=== BÁO CÁO CUỐI CÙNG ===\n";
        $this->generateFinalReport();
        
        echo "\n=== HOÀN THÀNH QUY TRÌNH ===\n";
        echo __('common.tat_ca_hard_text_da_duoc_xu_ly_va_chuyen_doi_thanh');
        echo __('common.cac_file_backup_backup_da_duoc_tao_cho_cac_file_go');
        echo __('common.kiem_tra_cac_file_langvi_va_langen_de_xem_ket_quan');
    }
    
    /**
     * Chạy chế độ preview (không thay đổi file)
     */
    public function preview()
    {
        echo __('common.chay_che_do_preview_nn');
        
        // Tìm hard text
        $hardTexts = $this->finder->findHardText('.');
        
        if (empty($hardTexts)) {
            echo __('common.khong_tim_thay_hard_text_naon');
            return;
        }
        
        // Hiển thị báo cáo chi tiết
        $this->finder->generateReport();
        
        echo "\n";
        $this->processor->generateReport();
        
        echo "\n";
        $this->translator->generateTranslationReport();
        
        echo "\n=== KẾT THÚC PREVIEW ===\n";
        echo __('common.de_thuc_su_thay_doi_files_chay_lai_script_voi_tham');
    }
    
    /**
     * Tạo báo cáo cuối cùng
     */
    private function generateFinalReport()
    {
        // Kiểm tra các file đã được tạo
        $viFiles = glob('lang/vi/*.php');
        $enFiles = glob('lang/en/*.php');
        $backupFiles = glob('*.backup') + glob('*/*.backup') + glob('*/*/*.backup');
        
        echo __('common.files_translation_tieng_viet_da_tao') . count($viFiles) . "\n";
        foreach ($viFiles as $file) {
            echo "  - $file\n";
        }
        
        echo __('common.nfiles_translation_tieng_anh_da_tao') . count($enFiles) . "\n";
        foreach ($enFiles as $file) {
            echo "  - $file\n";
        }
        
        echo __('common.nfiles_backup_da_tao') . count($backupFiles) . "\n";
        if (count($backupFiles) > 0) {
            echo __('common.cac_file_goc_da_duoc_backup_voi_extension_backupn');
        }
        
        // Thống kê translation keys
        $totalKeys = 0;
        foreach ($viFiles as $file) {
            $translations = include $file;
            if (is_array($translations)) {
                $totalKeys += count($translations);
            }
        }
        
        echo __('common.ntong_so_translation_keys_da_tao_totalkeysn');
    }
    
    /**
     * Khôi phục từ backup
     */
    public function restore()
    {
        echo "=== KHÔI PHỤC TỪ BACKUP ===\n\n";
        
        $backupFiles = [];
        $this->findBackupFiles('.', $backupFiles);
        
        if (empty($backupFiles)) {
            echo __('common.khong_tim_thay_file_backup_naon');
            return;
        }
        
        echo __('common.tim_thay_1') . count($backupFiles) . " file backup:\n";
        
        foreach ($backupFiles as $backupFile) {
            $originalFile = substr($backupFile, 0, -7); // Loại bỏ .backup
            
            if (file_exists($originalFile)) {
                copy($backupFile, $originalFile);
                echo __('common.da_khoi_phuc_originalfilen');
            }
        }
        
        echo __('common.nhoan_thanh_khoi_phuc_tu_backupn');
    }
    
    /**
     * Tìm tất cả file backup
     */
    private function findBackupFiles($dir, &$backupFiles)
    {
        if (!is_dir($dir)) {
            return;
        }
        
        $items = scandir($dir);
        foreach ($items as $item) {
            if ($item === '.' || $item === '..') {
                continue;
            }
            
            $path = $dir . '/' . $item;
            
            if (is_dir($path)) {
                // Bỏ qua một số thư mục
                if (!in_array($item, ['vendor', 'node_modules', 'storage', 'bootstrap/cache'])) {
                    $this->findBackupFiles($path, $backupFiles);
                }
            } else {
                if (substr($path, -7) === '.backup') {
                    $backupFiles[] = $path;
                }
            }
        }
    }
}

// Xử lý command line arguments
$action = isset($argv[1]) ? $argv[1] : 'preview';

$script = new MainTranslationScript();

switch ($action) {
    case 'run':
        $script->run();
        break;
    case 'preview':
        $script->preview();
        break;
    case 'restore':
        $script->restore();
        break;
    default:
        echo __('common.cach_su_dungn');
        echo __('common.php_maintranslationscriptphp_preview_xem_truoc_ket');
        echo __('common.php_maintranslationscriptphp_run_chay_thuc_su_thay');
        echo __('common.php_maintranslationscriptphp_restore_khoi_phuc_tu');
        break;
}

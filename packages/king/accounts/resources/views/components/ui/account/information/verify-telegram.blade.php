@php
    $isAndroid = is_numeric(strpos(strtolower($_SERVER["HTTP_USER_AGENT"]), "android")); 
    $isIPhone = is_numeric(strpos(strtolower($_SERVER["HTTP_USER_AGENT"]), "iphone")); 
    $isIPad = is_numeric(strpos(strtolower($_SERVER["HTTP_USER_AGENT"]), "ipad")); 
    $isIOS = $isIPhone || $isIPad;    

    $telegram = env('TELEGRAM_URL');

    if($isIOS){ 
        $telegram = env('TELEGRAM_URL_IOS');
    } elseif ($isAndroid){ 
        $telegram = env('TELEGRAM_URL_ANDROID');
    }
@endphp

<x-ui.popup id="verify-telegram-modal" icon="vendor/accounts/images/account/information/verify-telegram.svg"
    icon_alt="verify telegram" title=__('account.xac_minh_telegram')>
    <form id="telegram-form">
        <p class="text-sm text-neutral-850">Xin chào {{ (Auth::user()->fullname ?? (Auth::user()->username ?? '')) }}. Vui lòng xác thực Telegram theo
            các bước sau</p>
        <ul class="text-sm text-neutral-850 mt-2">
            <li class="mt-2">1. Cài đặt và tạo tài khoản
                <a href="{{ $telegram }}" target="_blank" class="font-medium text-secondary-500">Telegram</a>
            </li>
            <li class="mt-2">2. Nhấn vào nút
                <a href="{{ env('TELEGRAM_ID_BOT_URL') }}" target="_blank" class="font-medium text-secondary-500">Lấy mã kích hoạt</a>
            </li>
            <li class="mt-2">3. Nhập mã kích hoạt trong Telegram vào bên dưới</li>
        </ul>
        <div class="mt-6">
            <x-kit.input type="tel" inputmode="numeric" inPopup label=__('account.ma_otp_1') id="telegram" name="code" placeholder=__('auth.nhap_ma_otp')
                maxlength="6" 
                oninput="formatNumber(this)"
                isRequire
                class="[&_div]:bg-neutral [&_div]:border-neutral-150">
            </x-kit.input>
        </div>
        <x-kit.button disabled button-type="submit" class="w-full capitalize mt-6">Xác minh </x-kit.button>
        <p class="text-center mt-6 text-sm text-neutral-800">Lưu ý: Sau khi xác thực thành công, Quý Khách có thể khôi
            phục mật khẩu bằng Telegram trong trường hợp SĐT không khả dụng.</p>
    </form>
</x-ui.popup>

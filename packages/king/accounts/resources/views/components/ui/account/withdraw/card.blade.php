<div class="flex flex-col gap-[12px] xl:gap-[24px]">
    <form method="POST" id="withdraw_card_form" class="flex flex-col gap-[20px] pt-[16px] pb-[12px] px-[12px] bg-neutral rounded-[16px] xl:gap-[24px] xl:p-0">
        @csrf
    
        <div class="flex flex-col gap-[8px]">
            <!-- Network Selection -->
            <div class="text-[12px] leading-[18px] text-neutral-1000">Chọn nhà mạng</div>
            <div class="grid grid-cols-2 gap-[8px] xl:grid-cols-3">
                <input class="js-card-network-withdraw-input absolute opacity-0" name="to_telcom_code" value="VIETTEL">
                @foreach((array) ($withdrawData['phonecardList']['cardlist'] ?? []) as $key => $phonecard)
                    <div @class([
                        'relative flex justify-start items-center gap-[8px] h-[44px] py-[10px] px-[12px] border rounded-[12px] border-neutral-150 bg-neutral-50 [&.active]:border-info-500 [&.active]:bg-neutral ',
                        'active' => $key === 'VIETTEL',
                        $phonecard -> status === 0 ? 'cursor-not-allowed' : 'js-card-network-withdraw cursor-pointer',
                        ])
                        data-value="{{ $key }}"
                        data-card="{{ json_encode($phonecard) }}"
                    >
                        <div class="bg-neutral w-[24px] h-[24px] rounded-full flex items-center justify-center text-secondary-500 font-bold">
                            <img class="{{ $key === 'ZING' ? 'translate-y-[1px]' : '' }}" src="{{ asset('/asset/icons/account/withdraw/card/' . (strtolower($key)) . '.svg') }}" alt="{{ $key }}">
                        </div>
                        <div class="text-[14px] leading-[20px] font-medium capitalize text-neutral-800 [.active_&]:text-neutral-1000">{{ strtolower($key) }}</div>
                        <span class="absolute top-[4px] right-[4px] hidden w-[12px] h-[12px] [.active_&]:block">
                           <img src="{{ asset('asset/images/check-blue-circle.webp') }}" alt="check" class="w-full h-full"/>
                        </span>
                    </div>
                @endforeach
            </div>
        </div>
    
        <!-- Amount Selection -->
        <div class="flex flex-col gap-[8px]">
            <div class="text-[12px] leading-[18px] text-neutral-1000">Mệnh giá thẻ</div>
            <input class="js-card-amount-withdraw-input absolute opacity-0" name="card_amount_unit" type="number">
    
            <div class="js-card-amount-withdraw-list amount-group grid grid-cols-3 gap-[8px]">
                @foreach(($withdrawData['phonecardList']['cardlist']->VIETTEL->value_txt ?? []) as $index => $card)
                    <button type="button" class="js-card-amount-withdraw-item relative h-[48px] border border-neutral-150 bg-card-amount rounded-[8px] overflow-hidden [&.active]:border-info-500 [&.active_.card-label-amount]:text-secondary-500 [&.active]:bg-card-amount-active xl:h-[42px]"
                        data-value="{{ $card->key }}">
                        <p class="card-label-amount text-[12px] leading-[18px] text-neutral-1000 font-medium xl:text-[14px] xl:leading-[20px]">{{ $card->label }}</p>

                        @if ($card -> key === 100000 || $card -> key === 1000000)
                            <div class="absolute top-0 right-0">
                                <x-accounts::ui.account.label-amount type="proposal"></x-accounts::ui.account.label-amount>
                            </div>
                        @endif
                    </button>
                @endforeach
            </div>
    
        </div>
        <!-- Input Fields -->
        <div class="grid grid-cols-1 gap-[20px] xl:grid-cols-2">
            <div class="relative w-full">
                <x-kit.number-input 
                    class="withdraw-card-number" 
                    name="card_number" 
                    isRequire 
                    label=__('account.so_luong_the') 
                    max="99"
                    inputClassName="card-number"
                >
                </x-kit.number-input>
            </div>
            <div class="relative w-full">
                <x-kit.input 
                    oninput="this.value = this.value.replace(/[^0-9]/g, '');"
                    maxlength="5" 
                    class="withdraw-phone" 
                    name="phone" 
                    isRequire 
                    type="tel" 
                    label=__('auth.so_dien_thoai') 
                    placeholder=__('account.nhap_5_so_cuoi_dien_thoai_2')
                    noBackground
                    >
                </x-kit.input>
            </div>
        </div>
    
        @if(session('error'))
            <div class="text-red-500">{{ session('error') }}</div>
        @endif
    
        <x-kit.button 
            buttonType="submit"
            class="button-submit js-submit-withdraw-card w-full mx-auto xl:w-[220px]"
            disabled
        >
            Rút Tiền
        </x-kit.button>
    </form>
    <x-accounts::ui.account.withdraw.guide-card :contentClass="$withdrawData['isWithdraw'] ? 'hidden' : ''"/>
</div>

@pushOnce('scripts')
    <script>
        window.labelAmount = `<div class="absolute top-0 right-0"><x-accounts::ui.account.label-amount :type="'proposal'"></x-accounts::ui.account.label-amount></div>`;
    </script>

    @vite(['resources/js/withdraw/card.js'])
@endpushOnce

@php
if (!isset($depositData['activeEwallet'])) {
    $ewallets = $depositData['ewallets'];
    $activeEwallet = null;
    foreach ($ewallets as $key => $ewallet) {
        if (count($ewallet['data']) > 0) {
            $activeEwallet = $ewallet;
            break;
        }
    }
    $depositData['ewallets'] = $ewallets;
    $activeEwallet['data'] = array_map(function($item) {
        return  array_merge((array)$item, [
            'id' => $item->account_no,
            'value' => $item->account_no,
            'label' => $item->account_name . ' - ' . $item->account_no,
        ]);
    }, $activeEwallet['data']);

    $activeEwallet['value'] = [
        'id' => $activeEwallet['data'][0]['account_no'] ?? '',
        'value' => $activeEwallet['data'][0]['account_no'] ?? '',
        'label' => ($activeEwallet['data'][0]['account_name'] ?? '') . ' - ' . ($activeEwallet['data'][0]['account_no'] ?? ''),
    ];
} else {
   $activeEwallet =  $depositData['activeEwallet'] ;
}
$ewalletInfo = null;

if ($activeEwallet && count($activeEwallet['data']) > 0) {
    $ewalletInfo = $activeEwallet['data'][0];
}
@endphp
<div class="mx-auto">
    <!-- Payment Method Selector -->
    <div class="grid grid-cols-3 gap-4 mb-6">
        <!-- MOMO Option -->
        @foreach ($depositData['ewallets'] as $key => $ewallet)
                <div @class([
                'flex-1 justify-center relative border rounded-lg p-3 flex items-center border-neutral-200 gap-2 bg-neutral-200 relative js-ewallet-option-type',
                '!border-secondary-400 !border-[2px] !bg-neutral' => isset($activeEwallet) && $key === $activeEwallet['key'],
                'cursor-pointer' => count(value: $ewallet['data']) > 0,
                '!cursor-not-allowed' => count($ewallet['data']) <= 0,
            ])
                    data-value="{{ json_encode($ewallet['data']) }}" data-key="{{ $key }}">
                    <img src="{{ asset('/asset/icons/account/deposit/ewallet/icon-' . $key . (isset($activeEwallet) && $key === $activeEwallet['key'] ? '-active' : '') . '.svg') }}"
                        alt="{{ $ewallet['name'] }}" data-key="{{ $key }}" class="w-8 h-8">
                    <span class="font-semibold text-sm text-neutral-900 leading-[19.6px]">{{ $ewallet['name'] }}</span>
                    @if (count($ewallet['data']) <= 0)
                        <div
                            class="absolute -top-2 right-1 rounded-full w-12 h-[18px] bg-neutral-800 flex items-center justify-center">
                            <span class="text-neutral font-bold text-[0.625rem]">Bảo trì</span>
                        </div>
                    @endif
                </div>
        @endforeach
    </div>

    <!-- Account Details Card -->
    <div class="bg-secondary-50 rounded-xl p-6">
        <!-- Account Header -->
        <div class="mb-2">
            <div class="text-neutral-700 font-semibold text-xs mb-2">Tài khoản
                {{ $activeEwallet['name'] ?? '' }}</div>
            <x-kit.dropdown id="ewallet-account" name="ewallet-account" type="ewallet" class="bg-neutral" search=''
                value="{{ isset($ewalletInfo['account_no']) ? $ewalletInfo['account_no'] : '' }}"
                placeholder="Chọn tài khoản {{ isset($activeEwallet['name']) ? $activeEwallet['name'] : '' }}"
                :options="$activeEwallet['data']" />
        </div>

        <!-- Account Details -->
        <div class="bg-neutral rounded-xl flex gap-6 p-4">
            <!-- Left Side Details -->
            <div class="flex-1 flex-col gap-2 pr-4 pt-4">
                <div class="flex items-center justify-between">
                    <div class="flex items-center gap-1">
                        <div class="w-24 text-neutral-700 font-semibold text-xs leading-[19.6px]">Chủ tài khoản:</div>
                        <div class="text-neutral-900 font-semibold text-sm leading-[19.6px] min-w-40 js-ewallet-value js-ewallet-account-name">
                            {{ $ewalletInfo['account_name'] ?? '' }}
                        </div>
                    </div>
                    <button class="text-neutral-500 text-2xl js-copy-ewallet-btn" 
                        data-field="accountName"
                        data-text="{{ $ewalletInfo['account_name'] ?? '' }}">
                        <i class="icon-copy"></i>
                    </button>
                </div>

                <div class="flex items-center justify-between">
                    <div class="flex items-center gap-1">
                        <div class="w-24 text-neutral-700 font-semibold text-xs leading-[19.6px]">Số tài khoản:</div>
                        <div class="text-neutral-900 font-semibold text-sm leading-[19.6px] min-w-40 js-ewallet-value js-ewallet-account-no">
                            {{ $ewalletInfo['account_no'] ?? '' }}
                        </div>
                    </div>
                    <button class="text-neutral-500 text-2xl js-copy-ewallet-btn" 
                        data-field="accountNo"
                        data-text="{{ $ewalletInfo['account_no'] ?? '' }}">
                        <i class="icon-copy"></i>
                    </button>
                </div>

                <div class="flex items-center justify-between">
                    <div class="flex items-center gap-1">
                        <div class="w-24 text-neutral-700 font-semibold text-xs leading-[19.6px]">Nội dung:</div>
                        <div class="text-secondary-300 font-semibold text-sm leading-[19.6px] min-w-40 js-ewallet-value js-ewallet-code">
                            {{ $depositData['ewalletCode'] ?? '' }}
                        </div>
                    </div>
                    <button class="text-neutral-500 text-2xl js-copy-ewallet-btn" 
                        data-field="ewalletCode"
                        data-text="{{ isset($depositData['ewalletCode']) ? $depositData['ewalletCode'] : '' }}">
                        <i class="icon-copy"></i>
                    </button>
                </div>
            </div>

            <!-- QR Code -->
            <div class="flex flex-col items-center justify-center">
                <img src="{{ $ewalletInfo['qr_code'] ?? '' }}" 
                     alt="QR Code" 
                     class="w-[178px] h-[178px] mb-2 object-cover js-qr-code-ewallet {{ isset($ewalletInfo['qr_code']) ? '' : 'hidden' }}">
                <button class="bg-primary-500 text-sm px-4 py-2 rounded-lg text-neutral js-download-qr-btn {{ isset($ewalletInfo['qr_code']) ? '' : 'hidden' }}"
                    data-qr="{{ isset($ewalletInfo['qr_code']) ? $ewalletInfo['qr_code'] : '' }}">
                    Tải mã QR
                </button>
            </div>
        </div>
    </div>
</div>
@pushOnce('scripts')
    @vite(['resources/js/deposit/ewallet.js'])
@endpushOnce

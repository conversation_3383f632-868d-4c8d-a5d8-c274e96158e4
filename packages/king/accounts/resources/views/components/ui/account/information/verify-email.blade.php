<x-ui.popup id="verify-email-modal" icon="vendor/accounts/images/account/information/verify-email.svg" icon_alt="verify email"
    iconClass="w-[96px] h-[96px]"
    title=__('account.xac_minh_email') modalClass="min-h-[480px] xl:max-h-[445px] xl:min-h-auto">
    <div class="w-full">

        <form id="verify-account-email">
            {{-- input mail container --}}
            <p class="text-sm text-neutral-850 text-center">Nhập Email của bạn và nhận mã xác minh</p>
            <div class="mt-6">
                <x-kit.input isRequire label="Email" id="email" name="email" placeholder=__('account.nhap_email_cua_ban')
                    class="[&_div]:bg-neutral [&_div]:border-neutral-150">
                </x-kit.input>
            </div>
            <x-kit.button disabled button-type="button" id="mail-send-button" class="w-full capitalize mt-6">
                <PERSON><PERSON><PERSON></x-kit.button>
            <p class="text-center mt-6 text-sm text-neutral-800">
                Lưu ý: Sau khi xác thực thành công, quý khách có thể khôi phục mật khẩu bằng Email.</p>
        </form>

        <form id="verify-account-otp" class="hidden">
            {{-- input otp container --}}
            <p class="text-sm text-neutral-850 text-center">Nhập OTP đã gửi vào địa chỉ Email của bạn</p>
            <div class="mt-6">
                <x-kit.input isRequire label=__('account.ma_otp_1') id="otp" name="otp" placeholder=__('auth.nhap_ma_otp')
                    class="[&_div]:bg-neutral [&_div]:border-neutral-150">
                </x-kit.input>
            </div>
            <x-kit.button disabled button-type="button" id="otp-send-button" class="w-full capitalize mt-6">
                Xác minh</x-kit.button>
            <p class="text-center mt-6 text-sm text-neutral-800">Không nhận được? 
                <button id="resend-otp" type="button"
                    class="text-secondary-500 font-medium" disabled>Gửi lại OTP</button> 
                    <span id="countdown" class="text-danger-500"></span>
            </p>
        </form>
    </div>
</x-ui.popup>

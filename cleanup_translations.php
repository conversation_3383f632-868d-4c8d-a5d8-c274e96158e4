<?php

/**
 * Script để dọn dẹp và cải thiện kết quả translation
 */

class TranslationCleanup
{
    private $langDir = 'lang';
    private $languages = ['vi', 'en'];
    
    public function __construct()
    {
        // Khởi tạo
    }
    
    /**
     * Dọn dẹp tất cả translation files
     */
    public function cleanupAll()
    {
        echo "=== BẮT ĐẦU DỌN DẸP TRANSLATION FILES ===\n\n";
        
        foreach ($this->languages as $lang) {
            $this->cleanupLanguage($lang);
        }
        
        echo "\n=== HOÀN THÀNH DỌN DẸP ===\n";
    }
    
    /**
     * Dọn dẹp translation files của một ngôn ngữ
     */
    private function cleanupLanguage($lang)
    {
        echo "Dọn dẹp ngôn ngữ: $lang\n";
        
        $langPath = $this->langDir . '/' . $lang;
        if (!is_dir($langPath)) {
            echo "Thư mục không tồn tại: $langPath\n";
            return;
        }
        
        $files = glob($langPath . '/*.php');
        foreach ($files as $file) {
            $this->cleanupFile($file);
        }
    }
    
    /**
     * Dọn dẹp một file translation
     */
    private function cleanupFile($filePath)
    {
        echo "  Dọn dẹp file: $filePath\n";
        
        // Đọc file hiện tại
        $translations = include $filePath;
        if (!is_array($translations)) {
            echo "    Lỗi: File không phải array\n";
            return;
        }
        
        $originalCount = count($translations);
        $cleanedTranslations = [];
        
        foreach ($translations as $key => $value) {
            // Bỏ qua các key không hợp lệ
            if ($this->shouldSkipKey($key, $value)) {
                continue;
            }
            
            // Làm sạch key và value
            $cleanKey = $this->cleanKey($key);
            $cleanValue = $this->cleanValue($value);
            
            $cleanedTranslations[$cleanKey] = $cleanValue;
        }
        
        // Sắp xếp theo key
        ksort($cleanedTranslations);
        
        // Ghi lại file
        $this->writeTranslationFile($filePath, $cleanedTranslations);
        
        $newCount = count($cleanedTranslations);
        $removed = $originalCount - $newCount;
        echo "    Đã xóa $removed entries không hợp lệ ($originalCount -> $newCount)\n";
    }
    
    /**
     * Kiểm tra có nên bỏ qua key này không
     */
    private function shouldSkipKey($key, $value)
    {
        // Bỏ qua nếu value là array
        if (is_array($value)) {
            return true;
        }

        // Đảm bảo key và value là string
        $key = is_string($key) ? $key : (string)$key;
        $value = is_string($value) ? $value : (string)$value;

        // Bỏ qua key quá ngắn
        if (strlen($key) < 2) {
            return true;
        }

        // Bỏ qua key chỉ chứa ký tự đặc biệt
        if (preg_match('/^[^a-zA-Z0-9_]+$/', $key)) {
            return true;
        }

        // Bỏ qua regex patterns
        if (strpos($key, '/') === 0 || strpos($value, '/') === 0) {
            return true;
        }
        
        // Bỏ qua HTML fragments không có nghĩa
        if (preg_match('/^[<>"\'\[\]{}()]+$/', $value)) {
            return true;
        }
        
        // Bỏ qua các ký tự đơn lẻ tiếng Việt (từ script xử lý dấu)
        if (strlen($value) === 1 && preg_match('/[àáạảãâầấậẩẫăằắặẳẵèéẹẻẽêềếệểễìíịỉĩòóọỏõôồốộổỗơờớợởỡùúụủũưừứựửữỳýỵỷỹđĐ]/', $value)) {
            return true;
        }
        
        // Bỏ qua các pattern regex phức tạp
        if (strlen($value) > 100 && (strpos($value, '[') !== false || strpos($value, ']') !== false)) {
            return true;
        }
        
        // Bỏ qua value là "Array"
        if ($value === 'Array') {
            return true;
        }
        
        return false;
    }
    
    /**
     * Làm sạch key
     */
    private function cleanKey($key)
    {
        // Loại bỏ ký tự đặc biệt ở đầu và cuối
        $key = trim($key, '_-.');
        
        // Thay thế nhiều underscore liên tiếp bằng một
        $key = preg_replace('/_+/', '_', $key);
        
        return $key;
    }
    
    /**
     * Làm sạch value
     */
    private function cleanValue($value)
    {
        // Trim whitespace
        $value = trim($value);
        
        return $value;
    }
    
    /**
     * Ghi file translation
     */
    private function writeTranslationFile($filePath, $translations)
    {
        $content = "<?php\n\nreturn [\n";
        
        foreach ($translations as $key => $value) {
            $escapedKey = addslashes($key);
            $escapedValue = addslashes($value);
            $content .= "    '{$escapedKey}' => '{$escapedValue}',\n";
        }
        
        $content .= "];\n";
        
        file_put_contents($filePath, $content);
    }
    
    /**
     * Tạo báo cáo thống kê
     */
    public function generateReport()
    {
        echo "\n=== BÁO CÁO THỐNG KÊ TRANSLATION ===\n\n";
        
        foreach ($this->languages as $lang) {
            echo "Ngôn ngữ: $lang\n";
            
            $langPath = $this->langDir . '/' . $lang;
            $files = glob($langPath . '/*.php');
            
            $totalKeys = 0;
            foreach ($files as $file) {
                $translations = include $file;
                if (is_array($translations)) {
                    $count = count($translations);
                    $totalKeys += $count;
                    $filename = basename($file);
                    echo "  $filename: $count keys\n";
                }
            }
            
            echo "  Tổng: $totalKeys keys\n\n";
        }
    }
    
    /**
     * Tìm và sửa các translation keys trùng lặp
     */
    public function fixDuplicates()
    {
        echo "=== SỬA TRÙNG LẶP TRANSLATION KEYS ===\n\n";
        
        foreach ($this->languages as $lang) {
            echo "Kiểm tra trùng lặp trong ngôn ngữ: $lang\n";
            
            $langPath = $this->langDir . '/' . $lang;
            $files = glob($langPath . '/*.php');
            
            $allKeys = [];
            $duplicates = [];
            
            // Tìm trùng lặp
            foreach ($files as $file) {
                $translations = include $file;
                if (is_array($translations)) {
                    foreach ($translations as $key => $value) {
                        if (isset($allKeys[$key])) {
                            $duplicates[$key][] = $file;
                            $duplicates[$key][] = $allKeys[$key];
                        } else {
                            $allKeys[$key] = $file;
                        }
                    }
                }
            }
            
            if (empty($duplicates)) {
                echo "  Không tìm thấy key trùng lặp\n";
            } else {
                echo "  Tìm thấy " . count($duplicates) . " key trùng lặp:\n";
                foreach ($duplicates as $key => $files) {
                    echo "    '$key' trong: " . implode(', ', array_unique($files)) . "\n";
                }
            }
            
            echo "\n";
        }
    }
}

// Chạy script nếu được gọi trực tiếp
if (basename(__FILE__) === basename($_SERVER['SCRIPT_NAME'])) {
    $cleanup = new TranslationCleanup();
    
    $action = isset($argv[1]) ? $argv[1] : 'cleanup';
    
    switch ($action) {
        case 'cleanup':
            $cleanup->cleanupAll();
            break;
        case 'report':
            $cleanup->generateReport();
            break;
        case 'duplicates':
            $cleanup->fixDuplicates();
            break;
        default:
            echo "Cách sử dụng:\n";
            echo "  php cleanup_translations.php cleanup    - Dọn dẹp translation files\n";
            echo "  php cleanup_translations.php report     - Tạo báo cáo thống kê\n";
            echo "  php cleanup_translations.php duplicates - Tìm key trùng lặp\n";
            break;
    }
}

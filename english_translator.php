<?php

/**
 * <PERSON><PERSON><PERSON> để dịch các text tiế<PERSON> sang tiếng Anh và tạo file lang/en
 */

require_once 'process_translations.php';

class EnglishTranslator
{
    private $translations = [];
    private $vietnameseTranslations = [];
    
    public function __construct()
    {
        $this->initializeTranslations();
    }
    
    /**
     * Khởi tạo từ điển dịch
     */
    private function initializeTranslations()
    {
        $this->translations = [
            // Auth & Account
            __('common.dang_nhap') => 'Login',
            __('common.dang_ky') => 'Sign Up',
            __('common.dang_xuat_1') => 'Logout',
            __('auth.ten_dang_nhap_1') => 'Username',
            __('auth.mat_khau_1') => 'Password',
            __('auth.mat_khau_moi_1') => 'New Password',
            __('auth.xac_nhan_mat_khau_1') => 'Confirm Password',
            __('auth.so_dien_thoai') => 'Phone Number',
            __('common.tai_khoan_1') => 'Account',
            __('common.tai_khoan_ngan_hang') => 'Bank Account',
            __('common.thong_tin_ca_nhan_1') => 'Personal Information',
            __('common.cap_nhat') => 'Update',
            __('account.xac_nhan') => 'Confirm',
            __('common.luu') => 'Save',
            __('common.huy') => 'Cancel',
            
            // Navigation & Menu
            __('common.trang_chu_9') => 'Home',
            __('common.tin_tuc_3') => 'News',
            __('common.tin_tuc_4') => 'News',
            __('account.khuyen_mai_1') => 'Promotions',
            __('common.tro_giup_7') => 'Help',
            __('common.lien_he_1') => 'Contact',
            __('common.ve_chung_toi_3') => 'About Us',
            __('common.gioi_thieu_nha_cai_1') => 'Casino Introduction',
            __('common.chinh_sach_bao_mat_4') => 'Privacy Policy',
            __('common.dieu_khoan_va_dieu_kien') => 'Terms and Conditions',
            __('common.huong_dan_2') => 'Guide',
            __('common.huong_dan_dang_ky') => 'Registration Guide',
            __('common.huong_dan_nap_tien') => 'Deposit Guide',
            __('common.huong_dan_rut_tien') => 'Withdrawal Guide',
            
            // Games & Casino
            __('common.cong_game_3') => 'Game Portal',
            __('account.song_bai_1') => 'Casino',
            __('common.the_thao_2') => 'Sports',
            __('common.lo_de_3') => 'Lottery',
            __('common.game_bai_4') => 'Card Games',
            __('account.ban_ca_1') => 'Fishing',
            __('account.no_hu_1') => 'Jackpot',
            __('common.choi_ngay') => 'Play Now',
            __('common.choi_ngay_1') => 'Play Now',
            __('common.cuoc_ngay') => 'Bet Now',
            'Game hot' => 'Hot Games',
            
            // Transaction
            __('common.nap_tien_1') => 'Deposit',
            __('common.rut_tien_1') => 'Withdraw',
            __('common.nap_ngay') => 'Deposit Now',
            __('common.so_du_vi') => 'Wallet Balance',
            __('account.giao_dich_1') => 'Transaction',
            __('common.lich_su') => 'History',
            __('common.lich_su_1') => 'History',
            __('common.lich_su_cuocgiao_dich') => 'Betting/Transaction History',
            
            // Common Actions
            __('common.xem_them_2') => 'See More',
            __('common.xem_chi_tiet') => 'See Details',
            __('common.tim_kiem') => 'Search',
            __('common.tai_them') => 'Load More',
            __('common.chon') => 'Select',
            __('common.chon_loai_games') => 'Select Game Type',
            __('common.dong') => 'Close',
            __('common.mo') => 'Open',
            
            // Status & Messages
            __('common.thanh_cong') => 'Success',
            __('account.that_bai_7') => 'Failed',
            __('account.dang_xu_ly_31') => 'Processing',
            __('account.hoan_thanh_7') => 'Completed',
            __('common.huy_bo') => 'Cancelled',
            __('common.cho_xu_ly') => 'Pending',
            __('common.tin_tuc_khong_kha_dung') => 'News not available',
            __('news.khong_co_du_lieu') => 'No data available',
            __('common.he_thong_dang_bao_tri') => 'System under maintenance',
            
            // Language
            __('common.tieng_viet') => 'Vietnamese',
            'English' => 'English',
            
            // Time & Date
            __('common.hom_nay') => 'Today',
            __('common.hom_qua') => 'Yesterday',
            __('common.tuan_nay') => 'This Week',
            __('common.thang_nay') => 'This Month',
            __('common.nam_nay') => 'This Year',
            
            // Numbers & Quantity
            __('account.tat_ca_3') => 'All',
            __('common.khong_co') => 'None',
            __('common.nhieu') => 'Many',
            'Ít' => 'Few',
            __('common.mot') => 'One',
            'Hai' => 'Two',
            'Ba' => 'Three',
            
            // Greetings
            __('common.xin_chao') => 'Hello',
            __('common.chao_mung') => 'Welcome',
            __('common.cam_on') => 'Thank you',
            __('common.xin_loi') => 'Sorry',
            
            // Others
            __('common.danh_muc') => 'Categories',
            __('common.ngan_hang_lien_ket') => 'Affiliated Banks',
            __('common.ban_quyen') => 'Copyright',
            __('common.mien_trach_nhiem_3') => 'Disclaimer',
            __('common.cau_hoi_thuong_gap_4') => 'FAQ',
            __('common.truy_cap_nhanh') => 'Quick Access',
            __('common.tong_quan') => 'Overview',
            __('common.thong_tin') => 'Information',
            __('common.thiet_lap') => 'Settings',
            __('common.cai_dat') => 'Settings',
        ];
    }
    
    /**
     * Dịch text từ tiếng Việt sang tiếng Anh
     */
    public function translateText($vietnameseText)
    {
        // Kiểm tra trong từ điển trước
        if (isset($this->translations[$vietnameseText])) {
            return $this->translations[$vietnameseText];
        }
        
        // Thử dịch một số pattern cơ bản
        $text = $this->translateBasicPatterns($vietnameseText);
        
        // Nếu không dịch được, giữ nguyên text gốc
        return $text;
    }
    
    /**
     * Dịch một số pattern cơ bản
     */
    private function translateBasicPatterns($text)
    {
        $patterns = [
            __('common.tin_tuc_5') => 'News $1',
            __('common.huong_dan_3') => '$1 Guide',
            __('common.lich_su_2') => '$1 History',
            __('common.thong_tin_1') => '$1 Information',
            __('common.danh_sach') => '$1 List',
            __('common.quan_ly') => '$1 Management',
            __('common.cai_dat_1') => '$1 Settings',
            __('common.moi_nhat') => 'Latest $1',
            '/^(.+) hot$/' => 'Hot $1',
            __('common.noi_bat') => 'Featured $1',
        ];
        
        foreach ($patterns as $pattern => $replacement) {
            if (preg_match($pattern, $text, $matches)) {
                return preg_replace($pattern, $replacement, $text);
            }
        }
        
        return $text;
    }
    
    /**
     * Tạo file translation tiếng Anh từ processor
     */
    public function createEnglishTranslationFiles()
    {
        // Lấy Vietnamese translations từ processor
        $processor = new TranslationProcessor();
        $this->vietnameseTranslations = $processor->getVietnameseTranslations();
        
        // Nhóm translations theo file group
        $groups = [];
        foreach ($this->vietnameseTranslations as $key => $vietnameseText) {
            $parts = explode('.', $key, 2);
            $group = $parts[0];
            $subKey = $parts[1];
            
            if (!isset($groups[$group])) {
                $groups[$group] = [];
            }
            
            // Dịch sang tiếng Anh
            $englishText = $this->translateText($vietnameseText);
            $groups[$group][$subKey] = $englishText;
        }
        
        // Tạo hoặc cập nhật các file translation tiếng Anh
        foreach ($groups as $group => $translations) {
            $this->updateEnglishTranslationFile("lang/en/{$group}.php", $translations);
        }
    }
    
    /**
     * Cập nhật file translation tiếng Anh
     */
    private function updateEnglishTranslationFile($filePath, $newTranslations)
    {
        // Đọc file hiện tại nếu có
        $existingTranslations = [];
        if (file_exists($filePath)) {
            $existingTranslations = include $filePath;
            if (!is_array($existingTranslations)) {
                $existingTranslations = [];
            }
        }
        
        // Merge với translations mới
        $allTranslations = array_merge($existingTranslations, $newTranslations);
        
        // Sắp xếp theo key
        ksort($allTranslations);
        
        // Tạo nội dung file
        $content = "<?php\n\nreturn [\n";
        foreach ($allTranslations as $key => $value) {
            // Bỏ qua nếu value là array
            if (is_array($value)) {
                continue;
            }

            // Đảm bảo key và value là string
            $key = is_string($key) ? $key : (string)$key;
            $value = is_string($value) ? $value : (string)$value;

            $escapedKey = addslashes($key);
            $escapedValue = addslashes($value);
            $content .= "    '{$escapedKey}' => '{$escapedValue}',\n";
        }
        $content .= "];\n";
        
        // Tạo thư mục nếu chưa có
        $dir = dirname($filePath);
        if (!is_dir($dir)) {
            mkdir($dir, 0755, true);
        }
        
        // Ghi file
        file_put_contents($filePath, $content);
        echo __('common.da_taocap_nhat_file_filepathn');
    }
    
    /**
     * Tạo báo cáo dịch thuật
     */
    public function generateTranslationReport()
    {
        echo "=== BÁO CÁO DỊCH THUẬT ===\n\n";
        
        $totalTranslations = count($this->vietnameseTranslations);
        $translatedCount = 0;
        $untranslatedTexts = [];
        
        foreach ($this->vietnameseTranslations as $key => $vietnameseText) {
            $englishText = $this->translateText($vietnameseText);
            
            if ($englishText !== $vietnameseText) {
                $translatedCount++;
            } else {
                $untranslatedTexts[] = $vietnameseText;
            }
        }
        
        echo __('common.tong_so_text_can_dich_totaltranslationsn');
        echo __('common.da_dich_duoc_translatedcountn');
        echo __('common.chua_dich_duoc') . count($untranslatedTexts) . "\n\n";
        
        if (!empty($untranslatedTexts)) {
            echo __('common.cac_text_chua_dich_duocn');
            foreach ($untranslatedTexts as $text) {
                echo "  - \"$text\"\n";
            }
        }
    }
}

// Chạy script nếu được gọi trực tiếp
if (basename(__FILE__) === basename($_SERVER['SCRIPT_NAME'])) {
    $translator = new EnglishTranslator();
    
    echo __('common.bat_dau_dich_sang_tieng_anhn');
    $translator->createEnglishTranslationFiles();
    
    echo __('common.ntao_bao_cao_dich_thuatn');
    $translator->generateTranslationReport();
    
    echo __('common.nhoan_thanh_dich_sang_tieng_anhn');
}

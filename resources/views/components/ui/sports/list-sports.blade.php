@props(['listSports'])

<div class="flex flex-col gap-3 mb-4 xl:gap-4 xl:mb-12">
    <x-ui.title-section title=__('common.the_thao_6') titleHighlight=__('common.soi_dong') titleButton=""></x-ui.title-section>
    <ul class="grid grid-cols-2 gap-[6px] xl:grid-cols-4 xl:gap-5 list-sports">
        {{-- Other Sports Games --}}
        @foreach ($listSports as $sport)
            <li class="relative cursor-pointer flex-shrink-0 rounded-lg effect-hover xl:min-w-full min-w-[116px]"
                onclick="openSport({
                link: '{{ $sport['url'] }}',
                apiUrl: '{{ $sport['apiUrl'] ?? '' }}',
                loginRequired: '{{ $sport['loginRequired'] ?? false }}',
            })">
                <div class="group box-border relative overflow-hidden rounded-[6px] xl:aspect-[295/416] xl:rounded-[8px]">
                    <div class="block w-full xl:hidden">
                        <img src="{{ asset($sport['imgmb']) }}" class="w-full object-cover" loading="lazy"
                            alt="{{ $sport['name'] . '_imgmb' }}" />
                    </div>
                    <div class="hidden w-full xl:block">
                        <img src="{{ asset($sport['img']) }}"
                            class="w-full group-hover:opacity-0" loading="lazy"
                            alt="{{ $sport['name'] . '_img' }}" />
                        <img src="{{ asset($sport['imghover']) }}"
                            class="w-full absolute top-0 left-0 opacity-0 group-hover:opacity-100"
                            loading="lazy" alt="{{ $sport['name'] . '_imghover' }}" />
                    </div>
                </div>
            </li>
        @endforeach
    </ul>
</div>


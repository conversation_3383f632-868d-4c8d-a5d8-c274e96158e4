<div class="new-pass-form h-full w-screen md:max-w-[440px]">
    <div class="form-modal__background absolute top-0 left-0 block aspect-[390/294] min-w-full md:hidden">
        <img 
            alt="new-pass" 
            src="{{ asset('asset/images/auth/signup-mb.avif') }}" 
            class="min-w-full h-full">
        <button 
            class="absolute top-[12px] right-[12px] w-[24px] h-[24px] bg-auth-close rounded flex items-center justify-center"
            onclick="closeAuthModal()" 
        >
            <img 
                alt="close" 
                src="{{ asset('asset/images/close.avif') }}" 
                class="w-[10px] h-[10px] object-cover"/>
        </button>
        <div class="flex flex-col gap-y-2 top-[6.1875rem] left-[22px] absolute">
            <img 
                src="{{ asset('asset/images/brand/logo.svg') }}" 
                alt="logo brand" 
                class="w-[145px] aspect-[145/46]" />
            <p class="text-sm font-medium text-neutral-1000">Nhà cái trực tuyến hàng đầu</p>
        </div>
    </div>
    <div class="form-modal__wrap relative flex h-full rounded-t-[18px] overflow-hidden md:rounded-[18px]">
        <div class="relative z-0 flex flex-col items-center flex-grow py-[32px] px-[20px] bg-neutral md:p-[32px]">
            <div class="w-full">
                <div class="flex flex-col">
                    <p class="mb-[16px] text-[18px] leading-[26px] font-semibold text-neutral-1000 uppercase">Tạo Mật Khẩu Mới</p>
                    <form class="flex flex-col gap-[24px]" id="new-pass-form-modal">
                        <input name="email" class="input-email absolute opacity-0 invisible"/>
                        <input name="username" class="input-username absolute opacity-0 invisible"/>
                        <x-kit.input 
                            noBackground
                            inPopup 
                            oninput="formatNumber(this)" 
                            maxlength="6" 
                            placeholder=__('auth.nhap_ma_otp') 
                            label="OTP" 
                            inputClassName="xl:placeholder:!text-neutral-400 xl:placeholder:!text-neutral-600"
                            name="otp" 
                            isRequire 
                            rightIcon="icon-shield"
                            rightIconClass="!cursor-default"
                            >
                        </x-kit.input>
                        <x-kit.input 
                            noBackground
                            inPopup 
                            maxlength="32" 
                            class="input-password" 
                            placeholder=__('auth.mat_khau_moi_1') 
                            label=__('auth.mat_khau_moi_1') 
                            type="password" 
                            allowEmoji="{{false}}"
                            autocomplete="new-password" 
                            name="password" 
                            inputClassName="xl:placeholder:!text-neutral-400 xl:placeholder:!text-neutral-600"
                            isRequire 
                            isShowIconPass>
                        </x-kit.input>
                        <x-kit.input 
                            noBackground
                            inPopup 
                            maxlength="32" 
                            placeholder=__('auth.xac_nhan_mat_khau_1') 
                            label=__('auth.xac_nhan_mat_khau_1')
                            type="password" 
                            allowEmoji="{{false}}"
                            autocomplete="new-password" 
                            name="confirmPassword" 
                            inputClassName="xl:placeholder:!text-neutral-400 xl:placeholder:!text-neutral-600"
                            isRequire 
                            isShowIconPass>
                        </x-kit.input>
                        <p class="text-center text-[12px] leading-[18px] text-neutral-800">
                            Bạn chưa nhận được mã OTP? 
                            <a class="button-send-again text-primary-700 cursor-pointer font-medium">
                                Gửi Lại
                            </a>
                        </p>
                        <x-kit.button 
                            disabled 
                            class="button-submit w-full rounded-full" 
                            style="filled" 
                            buttonType="submit" 
                            type="primary" 
                            size="large">
                            Lưu
                        </x-kit.button>
                    </form>
                </div>               
            </div>
        </div>
        <button 
            class="absolute top-[10px] right-[10px] z-[1] hidden items-center justify-center w-[32px] h-[32px] bg-neutral-100 hover:bg-neutral-200 rounded-full md:flex"
            onclick="closeAuthModal()" 
        >
            <img 
                alt="close" 
                src="{{ asset('asset/images/close-gold.avif') }}" 
                class="w-[8px] aspect-square"/>
        </button>
    </div>
</div>

<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;
use Carbon\Carbon;

class DetectEvent
{
    public function handle(Request $request, Closure $next): Response
    {
        if ($request->is('api/*') || $request->expectsJson()) {
            return $next($request);
        }

        $timeServer = Carbon::now('Asia/Ho_Chi_Minh');

        $startRewardEvent = config('rewardgoldenhour.startRewardEvent');
        $endRewardEvent = config('rewardgoldenhour.endRewardEvent');

        $startTopRacingEvent = config('topracing.startTopRacingEvent');
        $endTopRacingEvent = config('topracing.endTopRacingEvent');
        $daysPerBatch = config('topracing.daysPerBatch');

        $finalClubWorldCupEventStartTime = config('final-club-world-cup.startTime');
        $finalClubWorldCupEventEndTime = config('final-club-world-cup.endTime');

        $heroBanner = config('home.heroBanner.list');
        $rewardEventBanner = config('home.heroBanner.rewardEvent');
        $finalClubWorldCupEventBanner = config('home.heroBanner.finalClubWorldCupEvent');
        $topRacingValidBanner = config('home.heroBanner.topRacingEventValid');
        $topRacingExpireBanner = config('home.heroBanner.topRacingEventExpire');

        $data = [];

        // ===== Reward Event =====
        if ($startRewardEvent && $endRewardEvent) {
            $start = Carbon::createFromFormat('d/m/Y', $startRewardEvent)->startOfDay();
            $end = Carbon::createFromFormat('d/m/Y', $endRewardEvent)->endOfDay();

            $valid = $timeServer->between($start, $end);
            $before = $timeServer->lt($start);

            if ($valid || $before) {
                array_unshift($heroBanner, $rewardEventBanner);
            }

            $data['validRewardEvent'] = $valid;
            $data['beforeRewardEvent'] = $before;
            $data['startRewardEvent'] = $startRewardEvent;
        }

        // ===== Top Racing Event =====
        if ($startTopRacingEvent && $endTopRacingEvent && $daysPerBatch) {
            $start = Carbon::createFromFormat('d/m/Y', $startTopRacingEvent)->startOfDay();
            $end = Carbon::createFromFormat('d/m/Y', $endTopRacingEvent)->endOfDay();

            $valid = $timeServer->between($start, $end);
            $before = $timeServer->lt($start);

            if ($valid || $before) {
                array_unshift($heroBanner, $topRacingValidBanner);
            } else {
                array_unshift($heroBanner, $topRacingExpireBanner);
            }

            $cacheKey = 'top_racing_list_time_' . md5($startTopRacingEvent . $endTopRacingEvent . $daysPerBatch);
            $listTime = cache()->rememberForever($cacheKey, function () use ($startTopRacingEvent, $endTopRacingEvent, $daysPerBatch) {
                return $this->splitDateRangeByDays($startTopRacingEvent, $endTopRacingEvent, $daysPerBatch);
            });

            $data['validTopRacingEvent'] = $valid;
            $data['beforeTopRacingEvent'] = $before;
            $data['startTopRacingEvent'] = $startTopRacingEvent;
            $data['listTime'] = $listTime;
        }

        // ===== final club world cup Event =====
        if ($finalClubWorldCupEventStartTime && $finalClubWorldCupEventEndTime) {
            $start = Carbon::createFromFormat('d/m/Y H:i', $finalClubWorldCupEventStartTime);
            $end = Carbon::createFromFormat('d/m/Y H:i', $finalClubWorldCupEventEndTime);

            $valid = $timeServer->between($start, $end);
            $before = $timeServer->lt($start);

            if ($valid || $before) {
                array_unshift($heroBanner, $finalClubWorldCupEventBanner);
            }

            $data['validFinalClubWorldCupEvent'] = $valid;
            $data['beforeFinalClubWorldCupEvent'] = $before;
            $data['startFinalClubWorldCupEvent'] = $finalClubWorldCupEventStartTime;
        }

        $data['listHeroBanner'] = $heroBanner;

        $request->merge($data);

        view()->share($data);

        return $next($request);
    }

    private function splitDateRangeByDays(string $startDate, string $endDate, int $daysPerBatch): array
    {
        $start = Carbon::createFromFormat('d/m/Y', $startDate)->startOfDay();
        $end = Carbon::createFromFormat('d/m/Y', $endDate)->endOfDay();

        $totalDays = $start->diffInDays($end) + 1;
        $fullBatches = intdiv($totalDays, $daysPerBatch);
        $remainder = $totalDays % $daysPerBatch;

        $result = [];
        $currentStart = $start->copy();

        for ($i = 0; $i < $fullBatches; $i++) {
            $extraDays = ($i === $fullBatches - 1) ? $remainder : 0;
            $currentEnd = $currentStart->copy()->addDays($daysPerBatch - 1 + $extraDays);

            if ($currentEnd > $end) {
                $currentEnd = $end->copy();
            }

            $result[] = [
                'icon' => '',
                'name' => __('common.dot_1') . ($i + 1) . ': ' . $currentStart->format('d/m') . ' → ' . $currentEnd->format('d/m'),
                'label' => __('common.dot_1') . ($i + 1) . ': ' . $currentStart->format('d/m') . ' → ' . $currentEnd->format('d/m'),
                'value' => $currentStart->format('d/m/Y') . '.' . $currentEnd->format('d/m/Y'),
                'start' => $currentStart->format('d/m/Y'),
                'end' => $currentEnd->format('d/m/Y'),
                'key' => $i + 1
            ];

            $currentStart = $currentEnd->copy()->addDay();
        }

        return $result;
    }
}

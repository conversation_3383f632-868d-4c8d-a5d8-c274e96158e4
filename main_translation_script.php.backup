<?php

/**
 * <PERSON>ript chính để xử lý toàn bộ quy trình translation
 * 1. Tìm hard text trong các file .php và .blade.php
 * 2. Tạo translation keys và file lang/vi
 * 3. <PERSON><PERSON><PERSON> sang tiếng Anh và tạo file lang/en
 * 4. Thay thế hard text bằng __() helper trong code
 */

require_once 'find_hard_text.php';
require_once 'process_translations.php';
require_once 'english_translator.php';

class MainTranslationScript
{
    private $finder;
    private $processor;
    private $translator;
    
    public function __construct()
    {
        echo "=== KHỞI TẠO TRANSLATION SCRIPT ===\n\n";
        
        $this->finder = new HardTextFinder();
        $this->processor = new TranslationProcessor();
        $this->translator = new EnglishTranslator();
    }
    
    /**
     * Chạy toàn bộ quy trình
     */
    public function run()
    {
        echo "Bắt đầu quy trình xử lý translation...\n\n";
        
        // Bước 1: Tìm hard text
        echo "=== BƯỚC 1: TÌM HARD TEXT ===\n";
        $hardTexts = $this->finder->findHardText('.');
        echo "Tìm thấy " . count($hardTexts) . " hard text\n\n";
        
        if (empty($hardTexts)) {
            echo "Không tìm thấy hard text nào. Kết thúc.\n";
            return;
        }
        
        // Hiển thị một số ví dụ
        echo "Một số ví dụ hard text tìm thấy:\n";
        $examples = array_slice($hardTexts, 0, 5);
        foreach ($examples as $example) {
            echo "  - \"{$example['text']}\" trong file {$example['file']} (dòng {$example['line']})\n";
        }
        echo "\n";
        
        // Bước 2: Tạo file translation tiếng Việt
        echo "=== BƯỚC 2: TẠO FILE TRANSLATION TIẾNG VIỆT ===\n";
        $this->processor->createVietnameseTranslationFiles();
        echo "\n";
        
        // Bước 3: Tạo file translation tiếng Anh
        echo "=== BƯỚC 3: TẠO FILE TRANSLATION TIẾNG ANH ===\n";
        $this->translator->createEnglishTranslationFiles();
        echo "\n";
        
        // Bước 4: Thay thế hard text trong code
        echo "=== BƯỚC 4: THAY THẾ HARD TEXT TRONG CODE ===\n";
        $this->processor->replaceHardTextInFiles();
        echo "\n";
        
        // Tạo báo cáo cuối cùng
        echo "=== BÁO CÁO CUỐI CÙNG ===\n";
        $this->generateFinalReport();
        
        echo "\n=== HOÀN THÀNH QUY TRÌNH ===\n";
        echo "Tất cả hard text đã được xử lý và chuyển đổi thành translation keys.\n";
        echo "Các file backup (.backup) đã được tạo cho các file gốc.\n";
        echo "Kiểm tra các file lang/vi/ và lang/en/ để xem kết quả.\n";
    }
    
    /**
     * Chạy chế độ preview (không thay đổi file)
     */
    public function preview()
    {
        echo "=== CHẠY CHẾ ĐỘ PREVIEW ===\n\n";
        
        // Tìm hard text
        $hardTexts = $this->finder->findHardText('.');
        
        if (empty($hardTexts)) {
            echo "Không tìm thấy hard text nào.\n";
            return;
        }
        
        // Hiển thị báo cáo chi tiết
        $this->finder->generateReport();
        
        echo "\n";
        $this->processor->generateReport();
        
        echo "\n";
        $this->translator->generateTranslationReport();
        
        echo "\n=== KẾT THÚC PREVIEW ===\n";
        echo "Để thực sự thay đổi files, chạy lại script với tham số 'run'\n";
    }
    
    /**
     * Tạo báo cáo cuối cùng
     */
    private function generateFinalReport()
    {
        // Kiểm tra các file đã được tạo
        $viFiles = glob('lang/vi/*.php');
        $enFiles = glob('lang/en/*.php');
        $backupFiles = glob('*.backup') + glob('*/*.backup') + glob('*/*/*.backup');
        
        echo "Files translation tiếng Việt đã tạo: " . count($viFiles) . "\n";
        foreach ($viFiles as $file) {
            echo "  - $file\n";
        }
        
        echo "\nFiles translation tiếng Anh đã tạo: " . count($enFiles) . "\n";
        foreach ($enFiles as $file) {
            echo "  - $file\n";
        }
        
        echo "\nFiles backup đã tạo: " . count($backupFiles) . "\n";
        if (count($backupFiles) > 0) {
            echo "Các file gốc đã được backup với extension .backup\n";
        }
        
        // Thống kê translation keys
        $totalKeys = 0;
        foreach ($viFiles as $file) {
            $translations = include $file;
            if (is_array($translations)) {
                $totalKeys += count($translations);
            }
        }
        
        echo "\nTổng số translation keys đã tạo: $totalKeys\n";
    }
    
    /**
     * Khôi phục từ backup
     */
    public function restore()
    {
        echo "=== KHÔI PHỤC TỪ BACKUP ===\n\n";
        
        $backupFiles = [];
        $this->findBackupFiles('.', $backupFiles);
        
        if (empty($backupFiles)) {
            echo "Không tìm thấy file backup nào.\n";
            return;
        }
        
        echo "Tìm thấy " . count($backupFiles) . " file backup:\n";
        
        foreach ($backupFiles as $backupFile) {
            $originalFile = substr($backupFile, 0, -7); // Loại bỏ .backup
            
            if (file_exists($originalFile)) {
                copy($backupFile, $originalFile);
                echo "  Đã khôi phục: $originalFile\n";
            }
        }
        
        echo "\nHoàn thành khôi phục từ backup.\n";
    }
    
    /**
     * Tìm tất cả file backup
     */
    private function findBackupFiles($dir, &$backupFiles)
    {
        if (!is_dir($dir)) {
            return;
        }
        
        $items = scandir($dir);
        foreach ($items as $item) {
            if ($item === '.' || $item === '..') {
                continue;
            }
            
            $path = $dir . '/' . $item;
            
            if (is_dir($path)) {
                // Bỏ qua một số thư mục
                if (!in_array($item, ['vendor', 'node_modules', 'storage', 'bootstrap/cache'])) {
                    $this->findBackupFiles($path, $backupFiles);
                }
            } else {
                if (substr($path, -7) === '.backup') {
                    $backupFiles[] = $path;
                }
            }
        }
    }
}

// Xử lý command line arguments
$action = isset($argv[1]) ? $argv[1] : 'preview';

$script = new MainTranslationScript();

switch ($action) {
    case 'run':
        $script->run();
        break;
    case 'preview':
        $script->preview();
        break;
    case 'restore':
        $script->restore();
        break;
    default:
        echo "Cách sử dụng:\n";
        echo "  php main_translation_script.php preview  - Xem trước kết quả (không thay đổi file)\n";
        echo "  php main_translation_script.php run      - Chạy thực sự (thay đổi file)\n";
        echo "  php main_translation_script.php restore  - Khôi phục từ backup\n";
        break;
}

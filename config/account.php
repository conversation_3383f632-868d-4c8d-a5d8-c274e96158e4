<?php


return [
    'deposit' => [
        'promotions_desc' => [
            [
                'label' => __('account.khuyen_mai_1'),
                'desc' => '500,000 VND',
            ],
            [
                'label' => __('account.thuc_nhan_1'),
                'desc' => '1,000,000 VND',
            ],
            [
                'label' => __('account.so_vong_cuoc_1'),
                'desc' => __('account.25_vong_1'),
            ],
            [
                'label' => __('account.tien_cuoc_yeu_cau_1'),
                'desc' => '25,000 K',
            ],
        ],
        'tabs' => [
            [
                'id' => 'p2p',
                'title' => __('account.giao_dich_p2p_3'),
                'icon' => 'icon-p2p',
                'description' => 'P2P',
                'is_active' => true,
                'status_text' => __('account.moi_5'),
                'status' => 'new',
            ],
            [
                'id' => 'codepay',
                'title' => 'Codepay',
                'icon' => 'icon-quick-deposit',
                'description' => 'Codepay',
                'is_active' => true,
                'status_text' => __('account.de_xuat_1'),
                'status' => 'propose',
            ],
            [
                'id' => 'crypto',
                'title' => __('account.tien_ao_7'),
                'icon' => 'icon-crypto',
                'description' => __('account.tien_ao_7'),
                'is_active' => true,
                'status_text' => __('account.moi_5'),
                'status' => 'new',
            ],
            [
                'id' => 'ewallet',
                'title' => __('account.vi_dien_tu_3'),
                'icon' => 'icon-wallet',
                'description' => __('account.vi_dien_tu_3'),
                'is_active' => true,
                'status_text' => '',
                'status' => '',
            ],
            [
                'id' => 'card',
                'title' => __('account.the_cao_11'),
                'icon' => 'icon-phone-card',
                'description' => __('account.the_cao_11'),
                'is_active' => true,
                'status' => '',
                'status_text' => '',
            ],
        ],
    ],
    'withdraw_tabs' => [
        [
            'id' => 'p2p',
            'title' => __('account.giao_dich_p2p_3'),
            'icon' => 'icon-p2p',
            'description' => 'P2P',
            'is_active' => true,
            'status_text' => __('account.moi_5'),
            'status' => 'new',
        ],
        // [
        //     'id' => 'coin12',
        //     'title' => 'Coin12',
        //     'icon' => 'icon-coin12',
        //     'description' => 'Coin12',
        //     'is_active' => true,
        //     'status' => 'maintain'
        // ],
        [
            'id' => 'bank',
            'title' => __('account.ngan_hang_9'),
            'icon' => 'icon-bank',
            'description' => __('account.ngan_hang_9'),
            'is_active' => true,
        ],
        [
            'id' => 'crypto',
            'title' => __('account.tien_ao_7'),
            'icon' => 'icon-crypto',
            'description' => __('account.tien_ao_7'),
            'is_active' => true,
        ],
        [
            'id' => 'card',
            'title' => __('account.the_cao_11'),
            'icon' => 'icon-phone-card',
            'description' => __('account.the_cao_11'),
            'is_active' => true,
        ],
    ],
    'overview' => [
        'id' => 'overview',
        'href' => '/account',
        'icon' => 'vendor/accounts/images/account/menu/overview.svg',
        'name' => 'account.menus.overview_title',
        'active_list' => [],
    ],
    'transaction' => [
        [
            'id' => 'deposit',
            'href' => '/account/deposit',
            'icon' => 'vendor/accounts/images/account/menu/deposit.svg',
            'icon_active' => 'vendor/accounts/images/account/menu/deposit-active.svg',
            'name' => 'account.menus.deposit',
            'name_mb' => 'account.menus.deposit',
            'active_list' => [
                '/account/deposit/p2p',
                '/account/deposit/codepay',
                '/account/deposit/crypto',
                '/account/deposit/ewallet',
                '/account/deposit/card',
            ],
        ],
        [
            'id' => 'withdraw',
            'href' => '/account/withdraw',
            'icon' => 'vendor/accounts/images/account/menu/withdraw.svg',
            'icon_active' => 'vendor/accounts/images/account/menu/withdraw-active.svg',
            'name' => 'account.menus.withdraw',
            'name_mb' => 'account.menus.withdraw',
            'active_list' => [
                '/account/withdraw/coin12',
                '/account/withdraw/bank',
                '/account/withdraw/crypto',
                '/account/withdraw/card',
            ],
        ],
        [
            'id' => 'bank-management',
            'href' => '/account/bank-account',
            'icon' => 'vendor/accounts/images/account/menu/bank.svg',
            'icon_active' => 'vendor/accounts/images/account/menu/bank-active.svg',
            'name' => 'account.menus.bank_management',
            'name_mb' => 'account.menus.bank_management_mb',
            'active_list' => [],
        ],
        [
            'id' => 'history',
            'href' => '/account/history',
            'icon' => 'vendor/accounts/images/account/menu/history.svg',
            'icon_active' => 'vendor/accounts/images/account/menu/history-active.svg',
            'name' => 'account.menus.history',
            'name_mb' => 'account.menus.history_mb',
            'active_list' => [],
        ],
    ],
    'more' => [
        [
            'id' => 'personal_information',
            'href' => '/account/information',
            'icon' => 'vendor/accounts/images/account/menu/account.svg',
            'icon_active' => 'vendor/accounts/images/account/menu/account-active.svg',
            'name' => 'account.menus.personal_information',
            'active_list' => [],
        ],
        [
            'id' => 'promotion',
            'href' => '/account/promotion',
            'icon' => 'vendor/accounts/images/account/menu/promotion.svg',
            'icon_active' => 'vendor/accounts/images/account/menu/promotion-active.svg',
            'name' => 'account.menus.promotion',
            'active_list' => [],
        ],
    ],
    'information' => [
        [
            'id' => 'personal_information',
            'href' => '/account/information',
            'icon' => 'vendor/accounts/images/account/menu/profile.svg',
            'name' => 'account.menus.personal_information',
        ],
    ],
    'history' => [
        'tabList' => [
            [
                'label' => __('account.lich_su_cuoc_1'),
                'value' => 'bet',
                'key' => 'betting',
            ],
            [
                'label' => __('account.lich_su_giao_dich_1'),
                'value' => 'transaction',
                'key' => 'transaction',
            ],
        ],
        'betHeader' => [__('account.ma_giao_dich_3'), __('account.thoi_gian_3'), 'Game', __('account.so_tien_3'), __('account.thangthua_1'), 'Turnover', __('account.trang_thai_3')],
        'transactionHeader' => [__('account.thoi_gian_3'), __('account.giao_dich_1'), __('account.ngan_hang_9'), __('account.phuong_thuc_1'), __('account.so_tien_3'), __('account.ma_giao_dich_3'), __('account.trang_thai_3')],
        'statusBet' => [
            '' => __('account.tat_ca_3'),
            'ALL' => __('account.tat_ca_3'),
            'WIN' => __('account.thang_3'),
            'WON' => __('account.thang_3'),
            'LOSE' => 'Thua',
            'LOST' => 'Thua',
            'CANCEL' => __('account.huy_1'),
            'DRAW' => __('account.hoa_1'),
            'TIP' => 'Tip',
            'TIPS' => 'Tip',
            'RUNING' => __('account.dang_dien_ra_5'),
            'RUNNING' => __('account.dang_dien_ra_5'),
            'PENDING' => __('account.dang_cho_3'),
            'OPENED' => __('account.dang_dien_ra_5'),
            'HALF LOSE' => 'Thua 1/2',
            'HALF WON' => __('account.thang_12_3'),
            'HALF_LOSE' => 'Thua 1/2',
            'HALF_WON' => __('account.thang_12_3'),
            'BONUS' => 'Bonus',
            'BET' => __('account.dang_cho_3'),
        ],
        'typeTransaction' => [
            'WITHDRAW' => __('common.rut_tien_1'),
            'DEPOSIT' => __('common.nap_tien_1'),
        ],
        'methodTransaction' => [
            'ibanking' => __('account.ngan_hang_9'),
            'bank_account' => __('account.chuyen_khoan_3'),
            'nicepay' => 'Codepay',
            'phone_card' => __('account.the_cao_11'),
            'crypto' => 'Crypto',
            'daily_cashback_slot' => __('account.hoan_tra_slots_3'),
        ],
        'statusTransaction' => [
            'CANCEL' => __('account.that_bai_7'),
            'DRAFT' => __('account.dang_xu_ly_31'),
            'FINISHED' => __('account.hoan_thanh_7'),
            'APPROVED' => __('account.dang_xu_ly_31'),
            'WAITING' => __('account.dang_xu_ly_31'),
            'PENDING' => __('account.dang_xu_ly_31'),
            'PROCESSING' => __('account.dang_xu_ly_31'),
            'PHONE_CARD_PROCESSING' => __('account.dang_xu_ly_31'),
            'PHONE_CARD_PENDING' => __('account.dang_xu_ly_31'),
            'PHONE_CARD_FINISHED' => __('account.hoan_thanh_7'),
            'PHONE_CARD_CANCEL' => __('account.that_bai_7'),
            'PHONE_CARD_DRAFT' => __('account.dang_xu_ly_31'),
        ],
        'bankList' => [
            'MSB' => 'Maritime Bank',
            'VIETBANK' => 'VietBank',
            'DAB' => 'Vikki Digital Bank',
        ],
    ],
    'guide_crypto' => [
        [
            'name' => 'Binance',
            'image' => 'binance',
        ],
        [
            'name' => 'Coin12',
            'image' => 'coin12',
        ],
        [
            'name' => 'Huobi',
            'image' => 'huobi',
        ],
        [
            'name' => 'Remitano',
            'image' => 'remitano',
        ],
    ],
    'accountPageSpecial' => [
        'account/bank-account',
    ],
];

<?php

/**
 * <PERSON><PERSON><PERSON> để dịch các text tiế<PERSON> Vi<PERSON> sang tiếng Anh và tạo file lang/en
 */

require_once 'process_translations.php';

class EnglishTranslator
{
    private $translations = [];
    private $vietnameseTranslations = [];
    
    public function __construct()
    {
        $this->initializeTranslations();
    }
    
    /**
     * Khởi tạo từ điển dịch
     */
    private function initializeTranslations()
    {
        $this->translations = [
            // Auth & Account
            'Đăng nhập' => 'Login',
            'Đăng ký' => 'Sign Up',
            'Đăng xuất' => 'Logout',
            'Tên đăng nhập' => 'Username',
            'Mật khẩu' => 'Password',
            'Mật khẩu mới' => 'New Password',
            'Xác nhận mật khẩu' => 'Confirm Password',
            'Số điện thoại' => 'Phone Number',
            'Tài khoản' => 'Account',
            '<PERSON><PERSON><PERSON> khoản ngân hàng' => 'Bank Account',
            'Thông tin cá nhân' => 'Personal Information',
            'Cập nhật' => 'Update',
            '<PERSON><PERSON><PERSON> nhận' => 'Confirm',
            'Lưu' => 'Save',
            'Hủy' => 'Cancel',
            
            // Navigation & Menu
            'Trang chủ' => 'Home',
            'Tin tức' => 'News',
            'Tin Tức' => 'News',
            'Khuyến mãi' => 'Promotions',
            'Trợ giúp' => 'Help',
            'Liên hệ' => 'Contact',
            'Về chúng tôi' => 'About Us',
            'Giới thiệu nhà cái' => 'Casino Introduction',
            'Chính sách bảo mật' => 'Privacy Policy',
            'Điều khoản và điều kiện' => 'Terms and Conditions',
            'Hướng dẫn' => 'Guide',
            'Hướng dẫn đăng ký' => 'Registration Guide',
            'Hướng dẫn nạp tiền' => 'Deposit Guide',
            'Hướng dẫn rút tiền' => 'Withdrawal Guide',
            
            // Games & Casino
            'Cổng game' => 'Game Portal',
            'Sòng bài' => 'Casino',
            'Thể thao' => 'Sports',
            'Lô đề' => 'Lottery',
            'Game bài' => 'Card Games',
            'Bắn cá' => 'Fishing',
            'Nổ hũ' => 'Jackpot',
            'Chơi ngay' => 'Play Now',
            'Chơi Ngay' => 'Play Now',
            'Cược ngay' => 'Bet Now',
            'Game hot' => 'Hot Games',
            
            // Transaction
            'Nạp tiền' => 'Deposit',
            'Rút tiền' => 'Withdraw',
            'Nạp ngay' => 'Deposit Now',
            'Số dư ví' => 'Wallet Balance',
            'Giao dịch' => 'Transaction',
            'Lịch sử' => 'History',
            'Lịch Sử' => 'History',
            'Lịch Sử Cược/Giao Dịch' => 'Betting/Transaction History',
            
            // Common Actions
            'Xem thêm' => 'See More',
            'Xem chi tiết' => 'See Details',
            'Tìm kiếm' => 'Search',
            'Tải thêm' => 'Load More',
            'Chọn' => 'Select',
            'Chọn loại Games' => 'Select Game Type',
            'Đóng' => 'Close',
            'Mở' => 'Open',
            
            // Status & Messages
            'Thành công' => 'Success',
            'Thất bại' => 'Failed',
            'Đang xử lý' => 'Processing',
            'Hoàn thành' => 'Completed',
            'Hủy bỏ' => 'Cancelled',
            'Chờ xử lý' => 'Pending',
            'Tin tức không khả dụng' => 'News not available',
            'Không có dữ liệu' => 'No data available',
            'Hệ thống đang bảo trì' => 'System under maintenance',
            
            // Language
            'Tiếng Việt' => 'Vietnamese',
            'English' => 'English',
            
            // Time & Date
            'Hôm nay' => 'Today',
            'Hôm qua' => 'Yesterday',
            'Tuần này' => 'This Week',
            'Tháng này' => 'This Month',
            'Năm nay' => 'This Year',
            
            // Numbers & Quantity
            'Tất cả' => 'All',
            'Không có' => 'None',
            'Nhiều' => 'Many',
            'Ít' => 'Few',
            'Một' => 'One',
            'Hai' => 'Two',
            'Ba' => 'Three',
            
            // Greetings
            'Xin chào' => 'Hello',
            'Chào mừng' => 'Welcome',
            'Cảm ơn' => 'Thank you',
            'Xin lỗi' => 'Sorry',
            
            // Others
            'Danh mục' => 'Categories',
            'Ngân hàng liên kết' => 'Affiliated Banks',
            'Bản quyền' => 'Copyright',
            'Miễn trách nhiệm' => 'Disclaimer',
            'Câu hỏi thường gặp' => 'FAQ',
            'Truy cập nhanh' => 'Quick Access',
            'Tổng quan' => 'Overview',
            'Thông tin' => 'Information',
            'Thiết lập' => 'Settings',
            'Cài đặt' => 'Settings',
        ];
    }
    
    /**
     * Dịch text từ tiếng Việt sang tiếng Anh
     */
    public function translateText($vietnameseText)
    {
        // Kiểm tra trong từ điển trước
        if (isset($this->translations[$vietnameseText])) {
            return $this->translations[$vietnameseText];
        }
        
        // Thử dịch một số pattern cơ bản
        $text = $this->translateBasicPatterns($vietnameseText);
        
        // Nếu không dịch được, giữ nguyên text gốc
        return $text;
    }
    
    /**
     * Dịch một số pattern cơ bản
     */
    private function translateBasicPatterns($text)
    {
        $patterns = [
            '/^Tin tức (.+)$/' => 'News $1',
            '/^Hướng dẫn (.+)$/' => '$1 Guide',
            '/^Lịch sử (.+)$/' => '$1 History',
            '/^Thông tin (.+)$/' => '$1 Information',
            '/^Danh sách (.+)$/' => '$1 List',
            '/^Quản lý (.+)$/' => '$1 Management',
            '/^Cài đặt (.+)$/' => '$1 Settings',
            '/^(.+) mới nhất$/' => 'Latest $1',
            '/^(.+) hot$/' => 'Hot $1',
            '/^(.+) nổi bật$/' => 'Featured $1',
        ];
        
        foreach ($patterns as $pattern => $replacement) {
            if (preg_match($pattern, $text, $matches)) {
                return preg_replace($pattern, $replacement, $text);
            }
        }
        
        return $text;
    }
    
    /**
     * Tạo file translation tiếng Anh từ processor
     */
    public function createEnglishTranslationFiles()
    {
        // Lấy Vietnamese translations từ processor
        $processor = new TranslationProcessor();
        $this->vietnameseTranslations = $processor->getVietnameseTranslations();
        
        // Nhóm translations theo file group
        $groups = [];
        foreach ($this->vietnameseTranslations as $key => $vietnameseText) {
            $parts = explode('.', $key, 2);
            $group = $parts[0];
            $subKey = $parts[1];
            
            if (!isset($groups[$group])) {
                $groups[$group] = [];
            }
            
            // Dịch sang tiếng Anh
            $englishText = $this->translateText($vietnameseText);
            $groups[$group][$subKey] = $englishText;
        }
        
        // Tạo hoặc cập nhật các file translation tiếng Anh
        foreach ($groups as $group => $translations) {
            $this->updateEnglishTranslationFile("lang/en/{$group}.php", $translations);
        }
    }
    
    /**
     * Cập nhật file translation tiếng Anh
     */
    private function updateEnglishTranslationFile($filePath, $newTranslations)
    {
        // Đọc file hiện tại nếu có
        $existingTranslations = [];
        if (file_exists($filePath)) {
            $existingTranslations = include $filePath;
            if (!is_array($existingTranslations)) {
                $existingTranslations = [];
            }
        }
        
        // Merge với translations mới
        $allTranslations = array_merge($existingTranslations, $newTranslations);
        
        // Sắp xếp theo key
        ksort($allTranslations);
        
        // Tạo nội dung file
        $content = "<?php\n\nreturn [\n";
        foreach ($allTranslations as $key => $value) {
            // Bỏ qua nếu value là array
            if (is_array($value)) {
                continue;
            }

            // Đảm bảo key và value là string
            $key = is_string($key) ? $key : (string)$key;
            $value = is_string($value) ? $value : (string)$value;

            $escapedKey = addslashes($key);
            $escapedValue = addslashes($value);
            $content .= "    '{$escapedKey}' => '{$escapedValue}',\n";
        }
        $content .= "];\n";
        
        // Tạo thư mục nếu chưa có
        $dir = dirname($filePath);
        if (!is_dir($dir)) {
            mkdir($dir, 0755, true);
        }
        
        // Ghi file
        file_put_contents($filePath, $content);
        echo "Đã tạo/cập nhật file: $filePath\n";
    }
    
    /**
     * Tạo báo cáo dịch thuật
     */
    public function generateTranslationReport()
    {
        echo "=== BÁO CÁO DỊCH THUẬT ===\n\n";
        
        $totalTranslations = count($this->vietnameseTranslations);
        $translatedCount = 0;
        $untranslatedTexts = [];
        
        foreach ($this->vietnameseTranslations as $key => $vietnameseText) {
            $englishText = $this->translateText($vietnameseText);
            
            if ($englishText !== $vietnameseText) {
                $translatedCount++;
            } else {
                $untranslatedTexts[] = $vietnameseText;
            }
        }
        
        echo "Tổng số text cần dịch: $totalTranslations\n";
        echo "Đã dịch được: $translatedCount\n";
        echo "Chưa dịch được: " . count($untranslatedTexts) . "\n\n";
        
        if (!empty($untranslatedTexts)) {
            echo "Các text chưa dịch được:\n";
            foreach ($untranslatedTexts as $text) {
                echo "  - \"$text\"\n";
            }
        }
    }
}

// Chạy script nếu được gọi trực tiếp
if (basename(__FILE__) === basename($_SERVER['SCRIPT_NAME'])) {
    $translator = new EnglishTranslator();
    
    echo "Bắt đầu dịch sang tiếng Anh...\n";
    $translator->createEnglishTranslationFiles();
    
    echo "\nTạo báo cáo dịch thuật...\n";
    $translator->generateTranslationReport();
    
    echo "\nHoàn thành dịch sang tiếng Anh!\n";
}

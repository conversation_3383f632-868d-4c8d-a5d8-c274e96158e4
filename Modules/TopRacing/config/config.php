<?php
use App\Enums\UrlPathEnum;

$brand = config('app.brand_name');

return [
    'name' => 'TopRacing',
    'startTopRacingEvent' => env('START_TOP_RACING_EVENT_DAY', ''),
    'endTopRacingEvent' => env('END_TOP_RACING_EVENT_DAY', ''),
    'daysPerBatch' => env('DAYS_PER_BATCH_TOP_RACING_EVENT', 7),
    'listGift' => [
        'topracing:images/listGift/1.avif',
        'topracing:images/listGift/2.avif',
        'topracing:images/listGift/3.avif',
        'topracing:images/listGift/4.avif',
        'topracing:images/listGift/5.avif',
        'topracing:images/listGift/6.avif',
        'topracing:images/listGift/7.avif',
        'topracing:images/listGift/8.avif',
        'topracing:images/listGift/9.avif',
    ],
    'listTerm' => [
        [
            'type' => 'text',
            'content' => __('common.giai_thuong_se_duoc_cap_nhat_trong_vong_12_gio_ke')
        ],
        [
            'type' => 'list',
        ],
        [
            'type' => 'text',
            'content' => __('common.doi_tuong_ap_dung_chuong_trinh_danh_cho_tat_ca_tha') .  $brand . __('common.dang_huong_khuyen_mai_hoan_tra')
        ],
        [
            'type' => 'text',
            'content' => __('common.thanh_vien_huong_khuyen_mai_100_trong_thoi_gian_di')
        ],
        [
            'type' => 'text',
            'content' => __('common.cac_ve_can_keo_o_tat_ca_game_co_cuoc_2_ben_cac_ve')
        ],
        [
            'type' => 'text',
            'content' => __('common.doi_voi_cac_thanh_vien_co_hanh_vi_gian_lan') . $brand . __('common.co_quyen_huy_tu_cach_nhan_giai_thuong')
        ],
        [
            'type' => 'text',
            'content' => $brand . __('common.co_quyen_thay_doi_chinh_sua_hoac_huy_chuong_trinh')
        ],
        [
            'type' => 'text',
            'content' => __('common.dieu_khoan_va_dieu_kien_chung_cua_1') .  $brand . __('common.van_duoc_ap_dung_cho_chuong_trinh_nay_1')
        ],
    ],
    'listTypeInvolvement' => [
        [
            'link' => 'topracing:images/type-involvement/type-1.avif',
            'content' => __('common.thanh_vien_khong_can_dang_ky_tham_gia_chuong_trinh'),
            'class' => 'left-5'
        ],
        [
            'link' => 'topracing:images/type-involvement/type-2.avif',
            'content' => __('common.top_500_thanh_vien_co_tong_tien_dat_cuoc_cao_nhat') . $brand . '.',
            'class' => 'left-[19px]'
        ],
        [
            'link' => 'topracing:images/type-involvement/type-3.avif',
            'content' => __('common.dua_tren_tong_tien_dat_cuoc_thanh_cong_cua_nguoi_c'),
            'class' => 'left-5'
        ],
        [
            'link' => 'topracing:images/type-involvement/type-4.avif',
            'content' => __('common.tong_giai_thuong_cua_moi_vong_dua_top_se_duoc_tich'),
            'class' => 'left-[30px]'
        ],
    ],
    'listGame' => [
        [
            'type' => 'link',
            'link' => UrlPathEnum::SLOTS,
            'img' => 'topracing:images/applicable-games/1.avif'
        ],
        [
            'type' => 'link',
            'link' => UrlPathEnum::SPORTS,
            'img' => 'topracing:images/applicable-games/2.avif'
        ],
                [
            'type' => 'link',
            'link' => UrlPathEnum::GAME_OTHER,
            'img' => 'topracing:images/applicable-games/3.avif'
        ],
        [
            'type' => 'link',
            'link' => UrlPathEnum::CASINO,
            'img' => 'topracing:images/applicable-games/4.avif'
        ],
        [
            'type' => 'link',
            'link' => UrlPathEnum::GAME_CARD,
            'img' => 'topracing:images/applicable-games/5.avif'
        ],
        [
            'type' => 'link',
            'link' => UrlPathEnum::LOTTERY,
            'img' => 'topracing:images/applicable-games/6.avif'
        ],
        [
            'type' => 'game',
            'link' => UrlPathEnum::E_SPORTS,
            'img' => 'topracing:images/applicable-games/7.avif'
        ],
    ],
    'listTime' => [
        [
            'key' => '1',
            'name' => __('common.dot_1_0408_1008_1'),
            'value' => '04/08/2025.10/08/2025',
            'label' => __('common.dot_1_0408_1008_1'),
            'icon' => '',
            'start' => '04/08/2025',
            'end' => '10/08/2025',
        ],
        [
            'key' => '2',
            'name' => __('common.dot_2_1108_1708_1'),
            'value' => '11/08/2025.17/08/2025',
            'label' => __('common.dot_2_1108_1708_1'),
            'icon' => '',
            'start' => '11/08/2025',
            'end' => '17/08/2025',
        ],
        [
            'key' => '3',
            'name' => __('common.dot_3_1808_2408_1'),
            'value' => '18/08/2025.24/08/2025',
            'label' => __('common.dot_3_1808_2408_1'),
            'icon' => '',
            'start' => '18/08/2025',
            'end' => '24/08/2025',
        ],
        [
            'key' => '4',
            'name' => __('common.dot_4_2508_3108_1'),
            'value' => '25/08/2025.31/08/2025',
            'label' => __('common.dot_4_2508_3108_1'),
            'icon' => '',
            'start' => '25/08/2025',
            'end' => '31/08/2025',
        ],
        [
            'key' => '5',
            'name' => __('common.dot_5_0109_0709_1'),
            'value' => '01/09/2025.07/09/2025',
            'label' => __('common.dot_5_0109_0709_1'),
            'icon' => '',
            'start' => '01/09/2025',
            'end' => '07/09/2025',
        ],
        [
            'key' => '6',
            'name' => __('common.dot_6_0809_1409_1'),
            'value' => '08/09/2025.14/09/2025',
            'label' => __('common.dot_6_0809_1409_1'),
            'icon' => '',
            'start' => '08/09/2025',
            'end' => '14/09/2025',
        ],
        [
            'key' => '7',
            'name' => __('common.dot_7_1509_2109_1'),
            'value' => '15/09/2025.21/09/2025',
            'label' => __('common.dot_7_1509_2109_1'),
            'icon' => '',
            'start' => '15/09/2025',
            'end' => '21/09/2025',
        ],
        [
            'key' => '8',
            'name' => __('common.dot_8_2209_2809_1'),
            'value' => '22/09/2025.28/09/2025',
            'label' => __('common.dot_8_2209_2809_1'),
            'icon' => '',
            'start' => '22/09/2025',
            'end' => '28/09/2025',
        ],

          [
            'key' => '9',
            'name' => __('common.dot_9_2306_2906_1'),
            'value' => '23/06/2025.29/06/2025',
            'label' => __('common.dot_9_2306_2906_1'),
            'icon' => '',
            'start' => '23/06/2025',
            'end' => '29/06/2025',
        ],
    ],
];

<?php

namespace Modules\TopRacing\Http\Controllers;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Carbon\Carbon;
use Modules\TopRacing\Services\TopRacingService;

class TopRacingController extends Controller
{
    protected $topRacingService;

    public function __construct(TopRacingService $topRacingService)
    {
        $this->topRacingService = $topRacingService;
    }

    public function index()
    {
        $startTopRacingEvent = config('topracing.startTopRacingEvent');
        $endTopRacingEvent = config('topracing.endTopRacingEvent');

        $validEvent = config('events.statusEvent.valid');
        $beforeEvent = config('events.statusEvent.before');
        $expireEvent = config('events.statusEvent.expire');

        $validTopRacingEvent = request()->get('validTopRacingEvent');
        $beforeTopRacingEvent = request()->get('beforeTopRacingEvent');
        $listTime = request()->get('listTime') ?? [];

        $currentTime = Carbon::now('Asia/Ho_Chi_Minh');
        $defaultTime = null;
        $indexTime = 0;
        $status = null;
        $countdown = [];
        $topRankList = [];
        $restRankLink = [];
        $rankPosition = null;

        if ($startTopRacingEvent && $endTopRacingEvent) {
            if ($beforeTopRacingEvent) {
                $defaultTime = collect($listTime)->first();
                $indexTime = 0;
                $status = $beforeEvent;
            } else {
                if (!$validTopRacingEvent) {
                    $defaultTime = collect($listTime)->last();
                    $indexTime = array_key_last($listTime);
                    $status = $expireEvent;
                } else {
                    foreach ($listTime as $key => $time) {
                        $startDate = Carbon::createFromFormat('d/m/Y', $time['start']) -> startOfDay();
                        $endDate = Carbon::createFromFormat('d/m/Y', $time['end']) -> endOfDay();

                        $validEvent = $currentTime->between($startDate, $endDate);

                        if ($validEvent) {
                            $defaultTime = $time;
                            $indexTime = $key;
                            $status = $validEvent;
                        }
                    }
                }
            }
        
            if (!$defaultTime) {
                $defaultTime = collect($listTime)->last();
                $indexTime = array_key_last($listTime);
                $status = $expireEvent;
            }

            $valueTime = explode(".", $defaultTime['value'] ?? "") ?? [];

            $startTime = $this->formatDateToYMD($valueTime[0] ?? "");
            $endTime = $this->formatDateToYMD($valueTime[1] ?? "");

            $rankData = $this->topRacingService->getRankData($startTime, $endTime) ?? [];
            $rankList = $rankData->list ?? [];
            $rankPosition = $rankData->position ?? null;

            $topRankList = array_slice($rankList, 0, 3);
            $restRankLink = array_slice($rankList, 3);

            if ($status === $beforeEvent) {
                function countdownMilliseconds($startStr, $endStr) {
                    try {
                        $start = Carbon::createFromFormat('d/m/Y H:i:s', $startStr);
                        $end = Carbon::createFromFormat('d/m/Y H:i:s', $endStr);

                        if ($start->gte($end)) {
                            return __('common.ngay_bat_dau_phai_nho_hon_ngay_ket_thuc');
                        }
                        
                        $seconds = abs($start->diffInSeconds($end));
                        $days = floor($seconds / (60 * 60 * 24));
                        $hours = floor(($seconds % (60 * 60 * 24)) / (60 * 60));
                        $minutes = floor(($seconds % (60 * 60)) / 60);
                        $secs = $seconds % 60;

                        return [
                            'days' => $days > 0 && $days < 10 ? '0' . $days : $days,
                            'hours' => $hours,
                            'minutes' => $minutes,
                            'seconds' => $secs,
                        ];
                    } catch (\Exception $e) {
                        return __('common.loi') . $e->getMessage();
                    }
                }

                $countdown = countdownMilliseconds($currentTime->format('d/m/Y H:i:s'), $defaultTime['start'] . ' 00:00:00');
            }
        }

        return view('topracing::index', [
            'data' => [
                'listTime' => $listTime,
                'currentTime' => $currentTime,
                'defaultTime' => $defaultTime,
                'indexTime' => $indexTime,
                'status' => $status,
                'countdown' => $countdown,
                'topRankList' => $topRankList,
                'restRankLink' => $restRankLink,
                'rankPosition' => $rankPosition,
            ]
        ]);
    }

    private function formatDateToYMD($dateString) {
            $parts = explode("/", $dateString);
            if (count($parts) === 3) {
                return $parts[2] . '-' . $parts[1] . '-' . $parts[0];
            }
            return null;
    }
}

<?php

$brandName = config('app.brand_name', 'Z27');
$startDay = env('START_REWARD_EVENT_DAY');
$endDay = env('END_REWARD_EVENT_DAY');
$startHour = env('START_REWARD_EVENT_TIME');
$endHour = env('END_REWARD_EVENT_TIME');

return [
    'key' => 'li_xi_2025',
    'name' => 'RewardGoldenHour',
    'startRewardEvent' =>  $startDay,
    'endRewardEvent' =>  $endDay,
    'info' => [
        [
            'icon' => 'time',
            'title' => __('common.tu_span_classfontsemibold_textsecondary800') . $startHour . ' -> ' . $endHour .__('common.spanbrhang_ngay'),
        ],
        [
            'icon' => 'money',
            'title' => __('common.nap_tu_span_classfontsemibold_textsecondary800500'),
        ],
        [
            'icon' => 'gift',
            'title' => __('common.span_classfontsemibold_textsecondary800chi_200_gia'),
        ],
        [
            'icon' => 'product',
            'title' => __('common.ap_dung_brspan_classfontsemibold_textsecondary800d'),
        ],
    ],
    'rules' => [
        __('common.thoi_gian_dien_ra_chuong_trinh_0000') . $startDay . ' → 23:59, ' . $endDay . '.',
        __('common.nguoi_dung_nap_tien_tu_span_classfontsemibold') . $startHour . __('common.den') . $endHour . __('common.span_hang_ngay_voi_so_tien_tu_span_classfontsemibo'),
        __('common.sau_khi_nap_tien_thanh_cong_hop_qua_se_xuat_hien_d'),
        __('common.phan_thuong_se_duoc_trao_cho_span_classfontsemibol'),
        __('common.span_classfontsemiboldmoi_tai_khoan_chi_duoc_nhan'),
        __('common.nguoi_dung_phai_dat_doanh_thu_cuoc_hop_le_span_cla')
    ],
    'conditions' => [
        [
            'title' => __('common.doanh_thu_cuoc_hop_le_duoc_ap_dung_voi_tat_ca_cac') . $brandName . '.'
        ],
        [
            'title' => __('common.khong_ap_dung_cho_hinh_thuc_nap_p2p')
        ],
        [
            'title' => __('common.khong_ap_dung_chung_voi_goi_khuyen_mai_thuong_nap')
        ],
        [
            'title' => __('common.khi_tham_gia_chuong_trinh_nay_quy_khach_khong_the_1'),
            'list' => [
                __('common.game_bai'),
                'Table Games.',
                __('common.song_bai_truc_tuyen_live_casino_1'),
                __('common.tat_ca_game_tren_menu_mini_game_tai_xiu_roulette_m_1'),
                __('common.game_cua_cac_nha_cung_cap_pragmatic_play_tomhorn_q'),
                __('common.mot_so_game_cua_techplay_bao_gom_dua_ngua_may_xeng_1'),
                __('common.mot_so_game_no_hu_cua_techplay_bao_gom_dragonball'),
            ]
        ],
        [
            'title' => $brandName . __('common.co_quyen_cham_dut_tam_dung_hoac_huy_bo_chuong_trin')
        ],
        [
            'title' => __('common.cac_ve_cuoc_can_keo_co_ket_qua_hoa_bi_huy_cuoc_va')
        ],
        [
            'title' => __('common.dieu_khoan_va_dieu_kien_chung_cua_1') . $brandName . __('common.van_duoc_ap_dung_cho_chuong_trinh_nay_1')
        ],
    ],
    'event_status' => [
        'not_started' => 'not_started',
        'ended' => 'ended',
        'valid' => 'valid',
        'claimed' => 'claimed',
    ],
    'promotion_item' => [
        'id' => 'reward_golden_hour',
        'title' => $brandName . __('common.san_thuong_gio_vang'),
        'imgUrl' => Module::asset('rewardgoldenhour:images/promotion-reward-golden-hour.webp'),
        'imgUrlMb' => Module::asset('rewardgoldenhour:images/promotion-reward-golden-hour-mb.webp'),
        'actionUrl' => '/san-thuong-gio-vang',
    ],
    'hero_banner' => [
        'id' => 'reward_golden_hour',
        'title' => __('common.san_thuong_gio_vang_1'),
        'imgSrc' => Module::asset('rewardgoldenhour:images/banner-reward-golden-hour.avif'),
        'imgSrcMobile' => Module::asset('rewardgoldenhour:images/banner-reward-golden-hour-mb.avif'),
        'url' => '/san-thuong-gio-vang',
        'order' => 1,
        'type' => 'component',
    ],
];

